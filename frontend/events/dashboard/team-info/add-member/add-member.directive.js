angular.module('SportWrench').directive('addMember', addMember);

addMember.$inject = ['$timeout', '$http', 'GENDER_VALUES', 'SANCTIONING_BODY'];

function addMember($timeout, $http, GENDER_VALUES, SANCTIONING_BODY) {
    return {
        restrict: 'E',
        scope: {
            onMemberUpdate: '&',
            eventId: '@',
            club: '@',
            team: '@'
        },
        replace: true,
        templateUrl: 'events/dashboard/team-info/add-member/add-member.html',
        link: function (scope) {
            scope.GENDER_VALUES = GENDER_VALUES;

            var loadTimer = null;
            scope.utils = {
                searchbox: ''
            }
            scope.data = {
                members: []
            }
            scope.eventHasAAUSanctioning = scope.$parent.event.sport_sanctioning_id === SANCTIONING_BODY.AAU;

            scope.searchboxKeypress = function (e) {
                if(loadTimer)
                    $timeout.cancel(loadTimer);

                if(e.charCode === 13)
                    return __loadMembers();     

                loadTimer = $timeout(function () {
                    __loadMembers();
                }, 500);
            }

            scope.add = function (member, roleId, asStaff) { 
                if(member.master_athlete_id && member.team_name) {
                    __addMember(member, roleId, asStaff);
                } else {
                    __addMember(member, roleId, asStaff);
                }
            };

            function __addMember(member, roleId, asStaff) {
                $http.post('/api/event/' + scope.eventId + '/team/' + scope.team + '/add/member', {
                    role: roleId,
                    staff: member.master_staff_id,
                    athlete: member.master_athlete_id,
                    asStaff: asStaff
                }).success(function () {
                    scope.onMemberUpdate();
                    for(var i = 0, l = scope.data.members.length; i < l; ++i) {
                        if(scope.data.members[i].code === member.code) {
                            scope.data.members.splice(i, 1);
                            break;
                        }
                    }
                })
            }

            function __loadMembers () {
                if(!scope.utils.searchbox) {
                    scope.data.members = [];
                    return;
                }

                scope.utils.loading = true;
                $http.get(
                    '/api/event/' + scope.eventId + '/club/' + scope.club + '/members', {
                        params: {
                            search: scope.utils.searchbox,
                            exclude_team: scope.team
                        }
                    }
                )
                .success(function (data) {
                    scope.data.members = data.members
                }).finally(function () {
                    scope.utils.loading = false;
                })
            }
        }
    }
}
