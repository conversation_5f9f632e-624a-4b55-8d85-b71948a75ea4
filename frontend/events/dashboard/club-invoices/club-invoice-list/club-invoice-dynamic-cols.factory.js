angular.module('SportWrench').factory('ClubInvoiceDynamicColsFactory', ClubInvoiceDynamicColsFactory);

ClubInvoiceDynamicColsFactory.$inject = ['NG_TABLE_TOOLTIPS'];

function ClubInvoiceDynamicColsFactory(NG_TABLE_TOOLTIPS) {
    return {
        getColumns: function () {
            return [
                {
                    title           : 'Created',
                    name            : 'created',
                    sortable        : 'created',
                    visible         : true
                }, {
                    title           : 'Paid',
                    name            : 'paid',
                    sortable        : 'paid',
                    visible         : true
                },{
                    title           : 'Amount',
                    name            : 'amount',
                    sortable        : 'amount',
                    visible         : true
                }, {
                    title           : 'Type',
                    name            : 'type',
                    sortable        : 'type',
                    visible         : true
                }, {
                    title           : 'Status',
                    name            : 'status',
                    sortable        : 'status',
                    tooltip         : NG_TABLE_TOOLTIPS.REFUNDED,
                    visible         : true
                }, {
                    title           : 'Club Name',
                    name            : 'club_name',
                    sortable        : 'club_name',
                    visible         : true
                }
            ];
        }
    };
}
