angular.module('SportWrench').directive('embeddedEdit', embeddedEdit);

embeddedEdit.$inject = ['$timeout'];

function embeddedEdit($timeout) {
    return {
        restrict: 'E',
        scope: {
            value: '=',
            type: '@',
            options: '=',
            placeholder: '@',
            size: '@',
            hoverEditClass: '@',
            hoverViewClass: '@',
            onEditStart : '&',
            onEditEnd: '&',
            groupId: '@',
            readonly: '='
        }, 
        templateUrl: function (elem, attrs) {
            return (attrs.type === 'text')
                        ?'events/dashboard/tickets/embedded-edit/input.html'
                        :'events/dashboard/tickets/embedded-edit/select.html'
        },
        link: function (scope, elem) {
            scope.utils = {
                isEditModeOn: false,
                input: elem.find('[type="text"]'),
                hover: false
            };

            scope.onSelectEdit = function() {
                // fix: select onChange method is triggered before model is updated
                $timeout(function () {
                    scope.editFinished()
                })
            }

            scope.enableEditing = function () {
                if (!scope.readonly) {
                    scope.utils.isEditModeOn = true;
                    if (scope.type === 'text') {
                        var l = scope.value.length;
                        $timeout(function () {
                            scope.utils.input.focus();
                            scope.utils.input[0].setSelectionRange(l, l);
                        }, 0);
                    }
                }
            }

            scope.disableEditing = function () {
                 scope.utils.isEditModeOn = false;
            }

            scope.editFinished = function () {
                scope.onEditEnd({
                    callback: function (err) {
                        if(!err) scope.disableEditing();
                    }
                })
            }

            scope.editStart  = function () {
                scope.enableEditing();
                scope.onEditStart({
                    callback: function () {}
                })
            }

            scope.onKeypress = function (e) {
                if(e.charCode === 13) {
                    scope.editFinished();
                }
            }           

            scope.$on('embedded-edit.enable-edit', function (e, groupId) {
                if(groupId && scope.groupId) {
                    if(scope.groupId === groupId)
                        scope.enableEditing();
                }                
            });

            scope.$on('embedded-edit.disable-edit', function (e, groupId) {
                if(groupId && scope.groupId) {
                    if(scope.groupId === groupId)
                        scope.disableEditing();
                }  
            })
        }
    }
}
