angular.module('SportWrench').directive('ticketsTable', ticketsTable);

ticketsTable.$inject = [
    '$log', '$filter', 'DateService', '$uibModal', '_', 'ValidDatesService', 'ConfirmationService', 'moment',
    'toastr', 'PAYMENT_CONSTANTS', 'FEE_PAYER', 'StripeFeeService'
];

function ticketsTable (
    $log, $filter, DateService, $uibModal, _, ValidDatesService, ConfirmationService, moment, toastr, PAYMENT_CONSTANTS,
    FEE_PAYER, StripeFeeService
) {
    let CHANGE_DATE_FORMAT_LG               = 'EEE MM/dd hh:mm a';
    let CHANGE_DATE_UPDATE_FORMAT           = 'MM/dd/yyyy HH:mm+00';
    let CHANGE_DATE_UPDATE_FORMAT_WITH_TZ   = 'MM/dd/yyyy HH:mm';
    let WD_PASS                             = 'Weekend Passes';
    let D_PASS                              = 'Daily Passes';
    let CREATE_TICKET_MODE                  = 'create';
    let INITIAL_CHANGE_MODE                 = 'initial';
    let TICKET_TYPE                         = {
        DAILY   : 'daily',
        OTHER   : 'other',
        WEEKEND : 'weekend'
    };
    const SUB_TYPE = {
        DEFAULT: 'default',
        PARKING: 'parking'
    }
    return {
        restrict: 'E',
        scope: {
            tickets              : '=',
            changes              : '=',
            isCamps              : '=',
            camps                : '=?',
            initialPrice         : '=', // RENAME
            updateTicket         : '&',
            saveTicket           : '&',
            createChange         : '&',
            updateChange         : '&',
            removeChange         : '&',
            event                : '<',
            isAssignedTicketsMode: '<',
            useMerchandiseSales  : '<',
        },
        templateUrl: 'events/dashboard/tickets/tickets-table.html',
        link: function ($scope) {
            $scope.ticketModes = {};
            $scope.changesModes = {};
            $scope.timePickerOptions = {
                readonlyInput: true
            };

            $scope.pointOfSalesModeEnabled = $scope.event.allow_point_of_sales;

            $scope.utils = {};
            let defaultPublish = true;
            let dateFilter = $filter('date');

            $scope.editDescription = function (ticket) {
                $uibModal.open({
                    templateUrl : 'descriptionModal.html',
                    size        : 'sm',
                    controller: ['$scope', '$uibModalInstance', function ($scope, $uibModalInstance) {
                        $scope.descr = ticket.description;
                        $scope.save = function () {
                            if(getTicketMode(ticket) !== CREATE_TICKET_MODE) {
                                let temp = _.clone(ticket);
                                temp.description = $scope.descr;
                                ValidDatesService.formatValidDates(temp);

                                if($scope.useMerchandiseSales) {
                                    temp.ticket_type = TICKET_TYPE.DAILY;
                                }

                                updateTicket(_.omit(temp, 'bought', 'sw_fee'), function (err) {
                                    if(!err) {
                                        ticket.description = $scope.descr;
                                        $uibModalInstance.close();
                                    }
                                });
                            } else {
                                ticket.description = $scope.descr;
                                $uibModalInstance.close();
                            }
                        }
                    }]
                })
            }

            $scope.notAllowToSetValidDates = function (ticket) {
                return ticket.ticket_type !== TICKET_TYPE.DAILY || _.isEmpty(ticket.valid_dates)
            }

            $scope.getValidDates = function (ticket) {
                if($scope.notAllowToSetValidDates(ticket)) {
                    return ['All Days'];
                }

                return ticket.valid_dates.map(validDate => {
                    return moment(validDate, 'YYYY-MM-DD').format('ddd, MMM D')
                })
            }

            $scope.openValidDatesModal = function (ticket) {
                if(ticket.ticket_type !== TICKET_TYPE.DAILY) {
                    return;
                }

                let eventDates  = $scope.event.days_dates;
                let tickets     = $scope.tickets;

                let updateTicketFn = updateTicket.bind($scope);

                ValidDatesService.openValidDatesModal(ticket, eventDates, tickets, updateTicketFn)
            }

            $scope.dataSaver = function (date) {
                return dateFilter(date, CHANGE_DATE_UPDATE_FORMAT_WITH_TZ);
            }

            let _headerDateFormat = function (date) {
                return dateFilter(date, CHANGE_DATE_FORMAT_LG);
            }

            $scope.headerDate = function (date) {
                if(date) {
                    return _headerDateFormat(date);
                }

                return 'edit date';
            }

            $scope.purchaseDateStart = function () {
                if($scope.initialPrice) {
                    return _headerDateFormat($scope.initialPrice);
                }

                return 'N/A';
            }

            $scope.openCalendar = function (e, prop) {
                e.preventDefault();
                e.stopPropagation();
                $scope.changesModes[prop].open = !$scope.changesModes[prop].open;
            }

            let _getCurrentChange = function () {
                let now = DateService.nowNormalized();
                let changeKey = null;
                if($scope.initialPrice && (now > $scope.initialPrice)) {
                    changeKey = INITIAL_CHANGE_MODE;
                    let keys = Object.keys($scope.changes);
                    for(let i = 0 ; i < keys.length; ++i) {
                        if(now > $scope.changes[keys[i]])
                            changeKey = keys[i];
                    }
                }
                return changeKey;
            }

            $scope.updateHeaderDate = function (type, val, oldVal) {
                if(!val) {
                    let result = confirm('Do you really want to delete price change?');
                    if(!result) {
                        $scope.changes[type] = new Date(oldVal);
                        return;
                    }
                    $scope.removeChange({
                        change: type,
                        cb: function () {
                        }
                    })
                } else {
                    $scope.utils.error_msg = undefined;
                    let data = {
                        key: type,
                        value: val
                    };

                    if(data.value)
                        data.value = $filter('date')(data.value, CHANGE_DATE_UPDATE_FORMAT);

                    $scope.updateChange({ data })
                        .then(() => {
                            $scope.changes[type] = val;
                            $scope.utils.currentPriceKey = _getCurrentChange();
                        })
                }
            }

            $scope.createHeaderDate = function () {
                if($scope.utils.creatingPriceChange) return;

                $scope.utils.error_msg = undefined;
                $scope.utils.creatingPriceChange = true;

                let changeNumber = Object.keys($scope.changes).length + 1;

                for(let i = 0; i < $scope.tickets.length; ++i) {
                    if(!$scope.tickets[i].prices) {
                        $scope.tickets[i].prices = {};
                    }

                    if(!$scope.tickets[i].prices[changeNumber]) {
                        $scope.tickets[i].prices[changeNumber] = {};
                    }
                }

                $scope.changesModes[changeNumber] = { open: false };

                $scope.changes[changeNumber] = new Date();
                $scope.changes[changeNumber].setHours(0);
                $scope.changes[changeNumber].setMinutes(0);

                if(changeNumber > 1) {
                    $scope.changes[changeNumber].setMonth(
                            $scope.changes[changeNumber - 1].getMonth(),
                            $scope.changes[changeNumber - 1].getDate() + 1
                    );
                }
                else if($scope.initialPrice) {
                    $scope.changes[changeNumber].setMonth(
                        $scope.initialPrice.getMonth(),
                        $scope.initialPrice.getDate() + 1
                    );
                }

                let changeToSend = _.pick($scope.changes, changeNumber);
                changeToSend[changeNumber] = dateFilter(changeToSend[changeNumber], CHANGE_DATE_UPDATE_FORMAT);

                $scope.createChange({
                    change: changeToSend,
                    cb: function (err) {
                        $scope.utils.creatingPriceChange = false;
                        if(err) {
                            $scope.changesModes[changeNumber] = undefined;
                            delete $scope.changes[changeNumber];
                            $scope.utils.error_msg = err;
                        } else {
                            $scope.utils.currentPriceKey = _getCurrentChange();
                        }
                    }
                })
            }

            let updateTicket = function (ticket, done) {
                if (ticket.position || ticket.position === 0) {
                    delete ticket.position;
                }

                ticket.border_color = ticket.border_color || null;

                $scope.ticketModes[ticket.event_ticket_id] = false;
                $scope.utils.error_msg = undefined;
                $scope.updateTicket({
                    ticket: ticket,
                    cb: function (err, result) {
                        if (!err) {
                            $scope.ticketModes[ticket.event_ticket_id] = undefined;
                        }

                        if (done) {
                            done(err, result);
                        }
                    }
                });
            }

            let createTicket = function (ticket, done) {
                let position = ticket.position;
                delete ticket.position;
                $scope.utils.error_msg = undefined;

                ticket.border_color = ticket.border_color || null;

                if($scope.useMerchandiseSales) {
                    ticket.ticket_type = TICKET_TYPE.DAILY;
                }

                $scope.saveTicket({
                    ticket: ticket,
                    cb: function (err, data) {
                        if (!err) {
                            let ticketID = data.event_ticket_id;

                            $scope.ticketModes[ticketID] = true;
                            $scope.ticketModes[ticketID] = false;
                            $scope.tickets[position].event_ticket_id = ticketID;
                        }

                        if (done) {
                            done(err, data);
                        }
                    }
                });
            }

            let _priceToInt = function (t) {
                t.initial_price = Number(t.initial_price);

                if (!t.prices) {
                    return;
                }

                for (let i = 0, keys = Object.keys(t.prices); i < keys.length; ++i) {
                    if(_.isObject(t.prices[keys[i]])) {
                        t.prices[keys[i]].value = Number(t.prices[keys[i]].value) || 0;
                    }
                }

                t.waitlist_switching_count = Number(t.waitlist_switching_count);
            }

            $scope.save = async function (ticket) {
                let ticketType = _.omit(ticket, 'bought', 'sw_fee');
                // set default sub_type
                ticketType.sub_type = ticketType.sub_type || SUB_TYPE.DEFAULT;
                ticketType.allow_reentry = !!ticketType.allow_reentry;

                const validateMinimumPrice = function(ticketData) {
                    // Get the price to validate (either initial price or last price change)
                    const getLastPriceValue = function(data) {
                        if ($scope.utils.currentPriceKey === INITIAL_CHANGE_MODE) {
                            return Number(data.initial_price) || 0;
                        } else if (data.prices && data.prices[$scope.utils.currentPriceKey]) {
                            return Number(data.prices[$scope.utils.currentPriceKey].value) || 0;
                        }
                        return Number(data.initial_price) || 0;
                    };

                    const price = getLastPriceValue(ticketData);

                    if (price === 0) return { valid: true };

                    const swFee = Number($scope.event.tickets_sw_fee) || 0;
                    const stripeTicketsFixedFee = Number($scope.event.stripe_tickets_fixed) || PAYMENT_CONSTANTS.DEFAULT_STRIPE_FIXED;
                    const stripeTicketsPercentFee = Number($scope.event.stripe_tickets_percent) || PAYMENT_CONSTANTS.DEFAULT_STRIPE_PERCENT;
                    const isDefaultStripeFeePayer = $scope.event.stripe_tickets_percent !== FEE_PAYER.BUYER;

                    const stripeFee = StripeFeeService.countCardFeeAmount(Number(price), stripeTicketsPercentFee / 100, stripeTicketsFixedFee, isDefaultStripeFeePayer);
                    const minPrice = swFee + stripeFee + PAYMENT_CONSTANTS.MINIMUM_TICKET_MARGIN;

                    return {
                        valid: price >= minPrice,
                        minPrice: minPrice.toFixed(2),
                        message: `Price must be greater than Service Fee. Minimum price: $${minPrice.toFixed(2)}`
                    };
                };

                const priceValidation = validateMinimumPrice(ticketType);

                if (!priceValidation.valid) {
                    toastr.warning(priceValidation.message, 'Invalid Price');
                    return;
                }

                let handleResp = function (err, result) {
                    if (result) {
                        if (_.isNumber(result.current_price) && result.current_price >= 0) {
                            ticket.current_price = result.current_price;
                        }

                        if (result.event_ticket_id) {
                            ticket.event_ticket_id = result.event_ticket_id;
                        }

                        if(!_.isUndefined(result.is_free)) {
                            ticket.is_free = result.is_free
                        }

                        if (result.sw_fee) {
                            ticket.sw_fee = result.sw_fee;
                        }
                        ticket.sub_type = ticketType.sub_type;
                    } else if (err) {
                        $scope.utils.error_msg = err;
                    }
                };

                _priceToInt(ticketType);

                if(_isFreeTicketCreation($scope.utils.currentPriceKey, ticketType)) {
                    let result = await ConfirmationService.ask('Are you sure you want to create a free ticket?', {
                        title 			: 'Confirm ticket creation:',
                        disableCancelBtn: true
                    });

                    if(result !== ConfirmationService.YES_RESP) {
                        return;
                    }
                }

                ValidDatesService.formatValidDates(ticketType);

                if(!ticket.event_ticket_id) {
                    createTicket(ticketType, handleResp);
                } else {
                    updateTicket(ticketType, handleResp);
                }
            }

            let _isFreeTicketCreation = function (currentPriceKey, ticketType) {
                let newPrice = currentPriceKey === INITIAL_CHANGE_MODE
                    ? ticketType.initial_price
                    : ticketType.prices &&
                        ticketType.prices[$scope.utils.currentPriceKey] &&
                        ticketType.prices[$scope.utils.currentPriceKey].value;

                return newPrice !== ticketType.current_price && Number(newPrice) === 0;
            }

            let _addTicket = function (label, shortLabel, type) {
                let keys = Object.keys($scope.changes),
                    prices = {},
                    valid_dates = {},
                    ticket = {},
                    i, l;

                for(i = 0, l = keys.length; i < l; ++i) {
                    prices[keys[i]] = {};
                }

                ticket.valid_dates = valid_dates;
                ticket.prices = prices;

                if(label) {
                    ticket.label = label;
                }
                if(shortLabel) {
                    ticket.short_label = shortLabel;
                }

                if(type) {
                    ticket.ticket_type = type;
                } else if ($scope.event.camps_sales) {
                    ticket.ticket_type = TICKET_TYPE.OTHER;
                }

                if(defaultPublish) {
                    ticket.published = defaultPublish;
                }

                ticket.position = $scope.tickets.length;
                ticket.allow_reentry = true;

                $scope.tickets.push(ticket);
            }

            $scope.addTicket = function () {
                _addTicket();
            }

            $scope.AddDefaultPasses = function () {
                _addTicket(D_PASS, 'DP', TICKET_TYPE.DAILY);
                _addTicket(WD_PASS, 'WP', TICKET_TYPE.WEEKEND);
            }

            $scope.getTicketMode = function (ticket) {
                return getTicketMode(ticket);
            }

            const getTicketMode = function (ticket) {
                if(!ticket.event_ticket_id){
                    return CREATE_TICKET_MODE;
                }

                return $scope.ticketModes[ticket.event_ticket_id];
            }

            $scope.isCellDisabled = function (key) {
                let pKey = parseInt(key, 10),
                    pCurrent = parseInt($scope.utils.currentPriceKey, 10)
                return (pKey < pCurrent)
            }

            $scope.initialCellDisabled = function (t) {
                if(getTicketMode(t) === CREATE_TICKET_MODE) {
                    return false;
                }

                return ($scope.initialPrice < $scope.changes[$scope.utils.currentPriceKey])
            }

            $scope.setTicketPublication = function (ticket) {
                ticket.published = !ticket.published;

                if(getTicketMode(ticket) === CREATE_TICKET_MODE) {
                    return;
                }

                $scope.save(ticket);
            }

            let _getPrevPrice = function (t, key) {
                if(!t.prices) {
                    return t.initial_price;
                }

                let numKey = +key - 1;

                if(!numKey || numKey <= 0) {
                    return t.initial_price;
                }

                let value = (t.prices[numKey] && t.prices[numKey].value);

                if(!value) {
                    return _getPrevPrice(t, numKey);
                }

                return value;
            }

            $scope.getPrevPrice = function (t, key) {
                return _getPrevPrice(t, key);
            }

            $scope.getCheckboxTitle = function (t) {
                return t.published ? 'Unpublish ticket' : 'Publish ticket';
            }

            $scope.setMinDate = function (key) {
                let keyInt = +key;

                if(keyInt <= 1) {
                    return $scope.initialPrice;
                }

                return $scope.changes[keyInt - 1];
            }

            $scope.setMaxDate = function (key) {
                if(!_.isEmpty($scope.changes)) {
                    if(key === INITIAL_CHANGE_MODE) {
                        return ($scope.changes[1] || null);
                    }

                    let keyInt = +key;
                    return ($scope.changes[keyInt + 1] || null);
                }

                return null;
            }

            $scope.canEdit = function(ticket) {
                let {
                    tickets_published,
                    tickets_visible,
                    is_tickets_purchase_open
                } = $scope.event;

                const disabled_purchases = tickets_published && tickets_visible;
                const add_mode = !ticket.event_ticket_id;

                return (!disabled_purchases || is_tickets_purchase_open) && !ticket.is_ticket_purchased || add_mode;
            };

            $scope.getTypeColumnDescription = function() {
                return `Choose correct ticket types to enable validation rules for same First and Last names.
                        Same First and Last names are not allowed for Weekend 
                        tickets and Weekend+Daily tickets for same purchase`
            }

            let _initModes = function () {
                let keys = Object.keys($scope.changes);

                for(let i = 0 ; i < keys.length; ++i) {
                    $scope.changesModes[keys[i]] = { open: false };
                }

                $scope.changesModes[INITIAL_CHANGE_MODE] = { open: false };
            }

            $scope.utils.currentPriceKey = _getCurrentChange();

            $scope.initialChangeModeIsEnabled = function () {
                return $scope.utils.currentPriceKey === 'initial';
            }

            _initModes();
        }
    }
}
