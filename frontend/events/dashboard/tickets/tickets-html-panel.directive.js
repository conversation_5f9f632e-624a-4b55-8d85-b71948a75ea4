angular.module('SportWrench').directive('htmlEditorPanel', htmlEditorPanel);

htmlEditorPanel.$inject = ['$uibModal', '$sce', 'CKEDITOR_OPTIONS'];

function htmlEditorPanel ($uibModal, $sce, CKEDITOR_OPTIONS) {
    return {
        restrict: 'E',
        scope: {
            info            : '=',
            onInfoChanged   : '&?',
            modalTitle      : '@',
            field           : '@?'
        },
        templateUrl: 'events/dashboard/tickets/tickets-html-panel.html',
        link: function (scope, elem, attrs) {
            scope.showInfo = function () {
                return $sce.trustAsHtml(scope.info || '<span class="text-grey">Click to edit</span>');
            }

            scope.openEditor = function () {
                $uibModal.open({
                    size            : 'lg',
                    templateUrl     : 'events/dashboard/tickets/editor-modal.html',
                    controller: ['$scope', '$uibModalInstance', function ($scope, $uibModalInstance) {
                        $scope.data     = scope.info;
                        $scope.options  = CKEDITOR_OPTIONS;
                        $scope.title    = scope.modalTitle;
                        $scope.isLoaded = false;
                        $scope.confirm  = {
                            show: false
                        }

                        $scope.saveChanges = function () {
                            if(attrs.onInfoChanged && scope.onInfoChanged) {
                                $scope.error = null;
                                var data = {};
                                data[scope.field] = $scope.data;
                                scope.onInfoChanged({
                                    data: data,
                                    callback: function (err) {
                                        if(err) $scope.error = err
                                        else {
                                            scope.info = $scope.data;
                                            $uibModalInstance.close();
                                        }
                                    }
                                })
                            } else {
                                scope.info = $scope.data;
                                $uibModalInstance.close();
                            }
                        }

                        var _openConfirm = function (agree, cancel) {
                            $scope.confirm.show = true;
                            $scope.confirm.agree = agree;
                            if(cancel)
                                $scope.confirm.cancel = cancel;
                        }

                        var _hideConfirm = function () {
                            $scope.confirm.show = false;
                            $scope.confirm.agree = null;
                            $scope.confirm.cancel = null;
                        }

                        $scope.disableSave = function () {
                            return ($scope.data === scope.info)
                        }

                        $scope.discardChanges = function () {
                            _openConfirm(function () {
                                $scope.data = scope.info;
                            })                            
                        }

                        $scope.discardAndClose = function () {
                            _openConfirm(function () {
                                $uibModalInstance.dismiss();
                            })                           
                        }

                        $scope.confirmAgree = function () {
                            if($scope.confirm.agree) $scope.confirm.agree()
                            _hideConfirm();
                        }

                        $scope.confirmCancel = function () {
                            if($scope.confirm.cancel) $scope.confirm.cancel()
                            _hideConfirm();
                        }

                        $scope.$on('ckeditor.ready', function() {
                            $scope.isLoaded = true;
                        });
                    }]
                })
            }
        }
    }
}
