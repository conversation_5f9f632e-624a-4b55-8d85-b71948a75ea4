angular.module('SportWrench').directive('receiptLink', receiptLink);

receiptLink.$inject = ['$window'];

function receiptLink($window) {
    return {
        restrict: 'E',
        scope: {
            showLink: '@',
            barcodeVis: '@',
            barcode: '@'
        },
        template: '<a href="" ng-click="openReceipt()" ng-if="showLink === \'true\'" ng-bind="::barcodeVis"></a>\
                   <span ng-bind="::barcodeVis" ng-if="showLink !== \'true\'"></span>',
        link: function (scope) {
            scope.openReceipt = function () {
                if(scope.showLink !== 'true') return;
                $window.open('tickets/receipt/' + scope.barcode, '_blank');
            }
        }
    }
}
