angular.module('SportWrench').directive('refundItemsList', refundItemsList);

refundItemsList.$inject = ['$compile', '$templateCache', 'CAMP_REGISTRATION_STATUS'];

function refundItemsList($compile, $templateCache, CAMP_REGISTRATION_STATUS) {
	let templateUrl = 'events/dashboard/tickets/payment/refund-item/',
		getTemplate = function (type) {
			switch(type) {
				case 'camps':
					return templateUrl + 'camps-list.html';
				case 'tickets':
					return templateUrl + 'tickets-list.html';
				default:
					throw new Error('Invalid Sales Type ' + type)
			}
		};
	return {
		restrict: 'E',
		scope: {
			list 			            : '=',
			scanAvailable 	            : '=',
            cancelParticipation         : '&',
            showCancelParticipationBtn  : '<'
		},
		link: function (scope, elem, attrs) {
			attrs.$observe('type', function (t) {
				if(!t) return;
				var templateHtml = $templateCache.get(getTemplate(t));
				elem.html($compile(templateHtml)(scope))
			});

            scope.cancelCampParticipation = function (isConfirmed, camp) {
			    if(isConfirmed) {
                    scope.cancelParticipation({camp})
                }
            };

            scope.isCanceledCamp = function (camp) {
                return camp.quantity === 0 || camp.registration_status === CAMP_REGISTRATION_STATUS.CANCELED
            };

            scope.showCancelButton = function (ticket) {
                return ticket.available > 0 &&
                    scope.showCancelParticipationBtn &&
                    ticket.registration_status !== CAMP_REGISTRATION_STATUS.CANCELED
            }
		}
	}
}
