angular.module('SportWrench').directive('redeemTickets', redeemTickets);

redeemTickets.$inject = ['INTERNAL_ERROR_MSG'];

function redeemTickets(INTERNAL_ERROR_MSG) {
	return {
		restrict: 'E',
		scope: {
			ticketsList : '=tickets',
			salesType 	: '@'
		},
		require 		: '^paymentModal',
		templateUrl 	: 'events/dashboard/tickets/payment/redeem.html',
		link: function (scope, elem, attrs, ctrl) {
			scope.labels 		= {};
			scope.redeemable 	= {};
			scope.ranges 		= {};
			scope.resultAlert 	= {};

			var __getRedeemList = function (tickets, redeemable) {
				var receipt = [];
				for(var i = 0, l = tickets.length, redeemableValue; i < l; ++i) {
					redeemableValue = redeemable[tickets[i].purchase_ticket_id];
					if(!redeemableValue || (redeemableValue === tickets[i].available))
						continue;
					receipt.push({
						purchase_ticket_id 	: tickets[i].purchase_ticket_id,
						count 				: redeemableValue
					});
				}
				return receipt;
			}, redeemInProgress = false;

			scope.isNotScanned = function (ticket) {
				return (ticket.available === ticket.quantity)
			}

			scope.label = function (ticket) {
				return (
					scope.label[ticket.purchase_ticket_id] ||
 	  			    (
 	  			    	scope.label[ticket.purchase_ticket_id] = (
 	 	  			    	(scope.salesType === 'camps')
 	 	  			    		?(ticket.camp_name + ' - ' + ticket.label)
 	 	  			    		:ticket.label
 	  			    	)
 	  			    )
				)
			}

			scope.availableTickets = function (ticket) {
				return scope.ranges[ticket.purchase_ticket_id] || (
					scope.ranges[ticket.purchase_ticket_id] = 
					(
						(ticket.available === ticket.quantity)
							?[ticket.available]
							:_.range(ticket.available, ticket.quantity + 1)
					)
				)
			}

			scope.disableRedeem = function () {
				return redeemInProgress;
			}

			scope.redeem = function () {
				var receipt = __getRedeemList(scope.ticketsList, scope.redeemable);
				
				if(_.isEmpty(receipt)) {
					scope.resultAlert.msg = 'Nothing to redeem'
					scope.resultAlert.type = 'warning'
					return;
				}

				ctrl.redeemTickets(receipt).then(function () {
					scope.resultAlert.msg = 'Success';
					scope.resultAlert.type = null;
				}, function (resp) {
					scope.resultAlert.msg = (resp && resp.data.validation) || INTERNAL_ERROR_MSG;
					scope.resultAlert.type = 'danger';
				}).then(function () {
					redeemInProgress = false;
				})
			}


		}
	}
}
