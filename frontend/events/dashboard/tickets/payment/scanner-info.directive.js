angular.module('SportWrench').directive('scannerInfo', scannerInfo);

scannerInfo.$inject = ['INTERNAL_ERROR_MSG', 'moment', 'userService', 'eventDashboardService'];

function scannerInfo(INTERNAL_ERROR_MSG, moment, userService, eventDashboardService) {
	return {
		restrict: 'E',
		scope: {
			scanner  : '<',
			scanned  : '<',
			location : '<',
            barcode  : '<'
		},
		require: '^paymentModal',
		templateUrl: 'events/dashboard/tickets/payment/scanner-info.html',
		link: function (scope, elem, attrs, ctrl) {
            const event = eventDashboardService.getEvent();
            scope.timezone = event.timezone;
            const format = 'YYYY-MM-DD HH:mm:ss';
		    const NOT_SCANNED = 'Not Scanned';

		    let showSuccessScanMsg;

            scope.picker = {
                isOpen: false,
                date: null,
                toggle() {
                    this.isOpen = !this.isOpen;
                },
                updateDate() {
                    this.date = new Date(moment.utc(Number(scope.scanned)).tz(event.timezone).format(format));
                },
                toTimestamp() {
                    const dateString = moment(this.date).format(format);
                    return moment.tz(dateString, format, event.timezone).valueOf();
                },
            };
			scope.resultAlert = {};

			scope.changeUpdateMode = function (value) {
			    if(value) {
			        scope.picker.updateDate();
                }
			    scope.isUpdateMode = value;
            };

			scope.showValue = function(value) {
			    return value || 'Not scanned';
            };

			scope.locationText = function () {
			    return scope.location || NOT_SCANNED;
            };

            scope.scannedText = function () {
			    return scope.scanned &&
                    moment.utc(Number(scope.scanned)).tz(event.timezone).format('MMM DD, YYYY h:mm a') || NOT_SCANNED;
            };

            scope.scannerText = function () {
			    return scope.scanner || NOT_SCANNED;
            };

            scope.manualScan = function (reset = false) {
                scope.scanner  = getUserName();
                scope.location = 'web';

                showSuccessScanMsg = true;

                scope.update(reset, Date.now());
            };

			scope.isScannedTicket = function () {
			    return scope.scanned && scope.scanner && scope.location;
            };

			scope.update = function (reset, date, update = false) {
				ctrl.updateScanner({
					scanner_id 			: scope.scanner,
					scanned_at 			: date,
					scanner_location 	: scope.location,
					reset 				: reset,
					updateMode          : update,
				}).then(function ({data}) {
					scope.resultAlert.msg = showSuccessScanMsg
                        ? `Ticket ${scope.barcode} marked as scanned`
                        : 'Success';

					scope.resultAlert.type = 'success';
					if(!reset) {
                        scope.scanned = new Date(Number(data.scanned_at));
                    }
                    scope.changeUpdateMode(false);

                    showSuccessScanMsg = false;
				}, function (resp) {
					if(resp && resp.data) {
						scope.resultAlert.msg = resp.data.validation || INTERNAL_ERROR_MSG;
						scope.resultAlert.type = 'danger'
					}
				})
			}

			function getUserName () {
                return userService.getName();
            }
		}
	}
})
