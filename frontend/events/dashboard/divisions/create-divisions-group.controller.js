angular.module('SportWrench').controller('Event.DivisionsWizardController', DivisionsWizardController);

DivisionsWizardController.$inject = [
    '$scope', '$uibModalInstance', '$state', '$stateParams', 'divisionsService', 'SANCTIONING_BODY',
    'INTERNAL_ERROR_MSG', 'loadEvent', 'divisionWizardTitles', 'USAV_SEASONALITY'
];

function DivisionsWizardController (
    $scope, $uibModalInstance, $state, $stateParams, divisionsService, SANCTIONING_BODY,
    INTERNAL_ERROR_MSG, loadEvent, divisionWizardTitles, USAV_SEASONALITY
) {
    $scope.event    = loadEvent.event;
    $scope.genders  = loadEvent.genders;
    $scope.ages     = divisionsService.getAges();
    $scope.titles   = divisionWizardTitles.get();

    let isEventHasUSAVSanctioning = $scope.event.sport_sanctioning_id === SANCTIONING_BODY.USAV;

    $scope.utils = {
        errors: [],
        total: 0,
        inProgress: false,
        createdCount: 0
    }

    $scope.create = function () {
        $scope.$broadcast('divisionsWizard.save');
    };

    $scope.createDivisions = function (divisions, errors = []) {
        $scope.utils.errors = errors;

        if (errors.length) {
            return;
        }

        $scope.utils.inProgress = true;
        _create(0, divisions, function() {      
            $scope.utils.inProgress = false;                        
            if($scope.utils.errors.length)
                return;
            else 
                $scope.closeModal();
        });
    };

    $scope.closeModal = function() {
        $uibModalInstance.close();
    }

    var __format = function (d) {
        if(d.max_teams) {
            d.max_teams = parseInt(d.max_teams, 10);
        }

        if(isEventHasUSAVSanctioning) {
            d.seasonality = USAV_SEASONALITY.FULL;
        }
    };

    function _create(index, divisions_arr, callback) {
        if (index >= divisions_arr.length) {
            return callback();
        }

        __format(divisions_arr[index]);
        
        divisionsService.createDivision($stateParams.event, divisions_arr[index])
        .error(function (data) {
            $scope.utils.errors.push( (data && data.validation) || INTERNAL_ERROR_MSG )
        }).finally(function () {
            if (index < divisions_arr.length) {
                ++$scope.utils.createdCount;
                _create(++index, divisions_arr, callback);
            }
        })
    }
}
