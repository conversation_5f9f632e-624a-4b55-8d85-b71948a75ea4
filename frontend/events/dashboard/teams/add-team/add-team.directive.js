angular.module('SportWrench').directive('assignTeam', assignTeam);

assignTeam.$inject = ['assignTeamService', 'divisionsService', 'toastr'];

function assignTeam(assignTeamService, divisionsService, toastr) {
    return {
        restrict: 'E',
        scope: {
            eventId: '@'
        },
        templateUrl: 'events/dashboard/teams/add-team/add-team.html',
        link: function (scope) {
            scope.utils = {
                divisions: []
            };

            scope.data = {
                teams: []
            }

            scope.params = {}

            scope.loadTeamsList = function () {
                assignTeamService.getTeamsList(this.eventId, this.params)
                .success(function (data) {
                    scope.data.teams = data.teams;
                }).error(__errorHandler)
            }

            scope.assignTeam = function (division_id, team) {
                assignTeamService.assignTeam(this.eventId, {
                    division_id:        division_id,
                    team_id:            team.master_team_id,
                    club_id:            team.master_club_id
                }).success(function () {
                    toastr.success('Team added!')
                    scope.loadTeamsList()
                }).error(__errorHandler)
            }

            loadDivisions();

            function loadDivisions () {
                divisionsService.getDivisions(scope.eventId, {
                    sort    : true
                })
                .then(function (divisions) {
                    scope.utils.divisions = divisions;
                }).then(null, __errorHandler)
            }

            function __errorHandler (data) {
                var msg = data && data.validation;
                if(msg) toastr.error(msg)
            }
        }
    }
}
