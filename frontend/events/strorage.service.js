angular.module('SportWrench').factory('eventStrorage', eventStrorage);

eventStrorage.$inject = ['$localStorage', '$state', 'APP_ROUTES'];

function eventStrorage($localStorage, $state, APP_ROUTES) {
    var storage = $localStorage;
    return {
        getLastVisited: function () {
            return storage && storage.eo_last_visited;
        },
        setLastVisited: function (name, params) {
            if(storage) {
                storage.eo_last_visited = {
                    name: name,
                    params: params
                };
            }
        },
        openLastVisited: function (id) {
            var lastVisited = storage && storage.eo_last_visited;

            if (_.isEmpty(lastVisited)) {
                $state.go(APP_ROUTES.EO.INFO, { event: id });
            } else {
                if (lastVisited.name === APP_ROUTES.EO.EMAIL_CONTACT_LIST) {
                    this.openLastVisitedContactList(lastVisited, id);
                } else if (lastVisited.name === APP_ROUTES.EO.OFFICIALS_PAYOUTS) {
                    this.openLastVisitedOfficialsPayouts(lastVisited, id);
                } else {
                    $state.go(lastVisited.name, { event: id });
                }
            }
        },
        setTeamsSorting: function (orderField, reverse) {
            if(storage) {
                if(!storage.eo_teams_sort) {
                    storage.eo_teams_sort = {};
                }
                storage.eo_teams_sort.field     = orderField;
                storage.eo_teams_sort.reverse   = reverse;
            }
        },
        getTeamsSorting: function () {
            return storage && storage.eo_teams_sort || {};
        },
        getLoggedInUserEmail: function () {
            return storage && storage.user.email;
        },
        setTicketsFilter: function (value, type, event) {
            if(storage) {
                if(!storage.eo_tickets_filter) {
                    storage.eo_tickets_filter = {};
                }
                if(!storage.eo_tickets_filter[event]) {
                    storage.eo_tickets_filter[event] = {}
                }
                storage.eo_tickets_filter[event][type] = value;
            }
        },
        getTicketsFilter: function (type, event) {
            return storage && storage.eo_tickets_filter && storage.eo_tickets_filter[event]
                && storage.eo_tickets_filter[event][type] || null;
        },
        openLastVisitedContactList: function(lastVisited, id) {
            if (_.isEmpty(lastVisited.params)) {
                $state.go(APP_ROUTES.EO.EMAIL_CONTACT_LISTS, { event: id });

                return;
            }

            const { list, event } = lastVisited.params;

            const lastVisitedEventID  = Number(event);
            const currentEventID      = Number(id);

            if (currentEventID !== lastVisitedEventID || !list) {
                $state.go(APP_ROUTES.EO.EMAIL_CONTACT_LISTS, { event: currentEventID });
            } else {
                $state.go(lastVisited.name, { event: currentEventID, list: list });
            }
        },
        openLastVisitedOfficialsPayouts: function(lastVisited, id) {
            const lastVisitedEventID = Number(lastVisited.params.event);
            const currentEventID = Number(id);

            if (lastVisitedEventID !== currentEventID) {
                $state.go(APP_ROUTES.EO.INFO, {
                    event: currentEventID,
                });
            } else {
                $state.go(lastVisited.name, { event: currentEventID });
            }
        }
    };
}
