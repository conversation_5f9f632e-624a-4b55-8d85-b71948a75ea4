angular.module('SportWrench').directive('socialLinksValidator', socialLinksValidator);

socialLinksValidator.$inject = ['URL_PATTERN'];

function socialLinksValidator(URL_PATTERN) {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.social = function (modelValue) {
                const prop = JSON.parse(attrs.socialLinksValidator);

                if (ctrl.$isEmpty(modelValue)) return true;
                if(prop.type === 'url' && prop.key !== 'snapchat') {
                    if(URL_PATTERN.test(modelValue))
                        return true;
                    return false;
                }
                return true;
            }
        }
    }
}
