angular.module('SportWrench').directive('timeValidator', timeValidator);

timeValidator.$inject = ['moment'];

function timeValidator(moment) {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            scope.validators = {};

            ctrl.$validators[attrs.name] = function (modelValue) {
                if (moment(modelValue).format('HH:mm') === '00:00') {
                    scope.validators[attrs.name] = 'Did you mean to set time to 23:59 instead of 00:00? Making it 00:00 will exclude selected date.';
                    return false;
                }

                delete scope.validators[attrs.name];
                return true;
            }
        }
    }
}
