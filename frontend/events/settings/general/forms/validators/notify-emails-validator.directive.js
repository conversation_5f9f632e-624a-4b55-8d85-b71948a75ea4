angular.module('SportWrench').directive('notifyEmailsValidator', notifyEmailsValidator);

notifyEmailsValidator.$inject = ['EMAIL_PATTERN'];

function notifyEmailsValidator(EMAIL_PATTERN) {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.emailsList = function (modelValue) {

                if (ctrl.$isEmpty(modelValue)) return true;

                var emails = modelValue.split(','),
                    email = '';

                for(var i = 0, l = emails.length; i < l; ++i) {
                    email = emails[i].trim();
                    if(!EMAIL_PATTERN.test(email)) return false;
                }     
                
                return true;
            }
        }
    }
}
