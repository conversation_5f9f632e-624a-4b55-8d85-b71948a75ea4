angular.module('SportWrench').directive('emailValidator', emailValidator);

emailValidator.$inject = ['EMAIL_PATTERN'];

function emailValidator(EMAIL_PATTERN) {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.email = function (modelValue) {
                if (ctrl.$isEmpty(modelValue)) return true;
                else return !!EMAIL_PATTERN.test(modelValue);
            }
        }
    }
}
