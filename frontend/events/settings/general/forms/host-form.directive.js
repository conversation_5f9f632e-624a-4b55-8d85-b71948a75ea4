angular.module('SportWrench').directive('hostForm', hostForm);

hostForm.$inject = ['SOCIAL_PATH', 'SOCIAL_NETWORKS'];

function hostForm(SOCIAL_PATH, SOCIAL_NETWORKS) {
    return {
        restrict: 'E',
        scope: {
            tournament: '='
        },
        templateUrl: 'events/settings/general/forms/host-form.html',
        replace: true,
        require: '^generalSettings',
        link: function (scope, attrs, elem, ctrl) {
            scope.utils = {
                networks: SOCIAL_NETWORKS,
                img_path: SOCIAL_PATH,
                formSubmitted: false,
                invalid_url_message: 'URL is incorrect. Example: http(s)://example.com',
                statesLoading: false,
                states: []
            }            

            scope.$on('EventSettingsForm.Submitted', function () {
                scope.utils.formSubmitted = true;
                var errors = [], socialKeys = Object.keys(scope.utils.networks), form = scope.hostForm;
                if(form.org_name.$invalid)
                    errors.push('Invalid Host Organization Name');
                if(form.host_address.$invalid)
                    errors.push('Invalid Host Address');
                if(form.host_city.$invalid)
                    errors.push('Invalid Host City');
                if(form.host_zip.$invalid)
                    errors.push('Invalid Host Zip');
                if(form.org_phone.$invalid)
                    errors.push('Host Organization Phone Invalid');
                if(form.website.$invalid)
                    errors.push('Host Organization Website Invalid');
                if(form.rules_website.$invalid)
                    errors.push('Rules Website URL Invalid')
                if(form.email.$invalid)
                    errors.push('Invalid Email');
                if(scope.showNotifyEmailsBox()) {
                    if(form.notify_emails.$invalid)
                        errors.push('Invalid Notification Emails String')
                }
                for(var i = 0, l = socialKeys.length, key = ''; i < l; ++i) {
                    key = socialKeys[i]
                    if(form[key].$invalid)
                        errors.push('Invalid "' + SOCIAL_NETWORKS[key].name + '" value')
                }                

                ctrl.setHostFormErrors(errors);
            })

            scope.socialValidatioMessage = function (type) {
                switch(type) {
                    case 'url':
                        return scope.utils.invalid_url_message
                    default:
                        return 'Invalid Username Value'
                }
            };

            scope.showNotifyEmailsBox = function () {
                return scope.tournament.notify_frequency !== 'never';
            };

            scope.getDataForValidator = (key, type) => {
                return { key, type };
            };

            var __loadStates = (function () {
                scope.utils.statesLoading = true;      
                ctrl.loadStates().then(function (data) {
                    scope.utils.states = data;                    
                }).finally(function () {
                    scope.utils.statesLoading = false;
                });                    
            })();

            var formName = 'host';

            ctrl.registerForm(formName);

            scope.$on('$destroy', function () {
                ctrl.unRegisterForm(formName);
            });
        }
    }
}
