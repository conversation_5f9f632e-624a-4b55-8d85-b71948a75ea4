angular.module('SportWrench').directive('boothsSettings', boothsSettings);

boothsSettings.$inject = ['EventSettingsService'];

function boothsSettings(EventSettingsService) {
    return {
        restrict: 'E',
        scope: {},
        templateUrl: 'events/settings/booths/booths-settings.html',
        require: '^eventSettings',
        link: function (scope, attrs, elem, ctrl) {
            var mode = ctrl.getMode(),
                eventId = ctrl.getEventId();
            scope.booths = [];
            // scope.$watch(function () {
            //     return ctrl.isLocationsActive();
            // }, function (activate) {
            //     if(activate === true && mode === 'update') {
            //         EventSettingsService.loadLocations(eventId)
            //     }
            // })
        }
    }
}
