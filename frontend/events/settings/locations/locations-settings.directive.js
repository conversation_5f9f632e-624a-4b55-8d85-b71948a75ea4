angular.module('SportWrench').directive('locationsSettings', locationsSettings);

locationsSettings.$inject = ['EventSettingsService', '$uibModal', 'toastr'];

function locationsSettings (EventSettingsService, $uibModal, toastr) {
    return {
        restrict: 'E',
        scope: {},
        templateUrl: 'events/settings/locations/locations-settings.html',
        require: '^eventSettings',
        link: function (scope, attrs, elem, ctrl) {
            var mode = ctrl.getMode(),
                eventId = ctrl.getEventId();
            scope.locations = [];
            scope.$watch(function () {
                return ctrl.isLocationsActive();
            }, function (activate) {
                if(activate === true && mode === 'update') {
                    __loadLocations()
                }
            }) 

            function __loadLocations () {
                ctrl.setLocationsLoading();
                EventSettingsService.loadLocations(eventId).success(function (data) {
                    scope.locations = data.locations;
                }).finally(function () {
                    ctrl.setLocationsLoaded();
                });
            }

            scope.createLocation = function () {
                $uibModal.open({
                    template: '<event-location-form event-id="' + eventId + '"></event-location-form>'
                }).result.then(function onClose (created) {
                    if(created) __loadLocations();
                })
            }

            scope.getLocationTitle = function (number) {
                return (number === 1)
                            ?'Main Location'
                            :('Location #' + number)
            }

            scope.editLocation = function (id) {
                $uibModal.open({
                    template: '<event-location-form id="' + id + '" event-id="' + eventId + '"></event-location-form>'
                }).result.then(function onClose (updated) {
                    if(updated) __loadLocations();
                })
            }

            scope.removeLocation = function (id) {
                $uibModal.open({
                    template:
                        '<div class="modal-header"><h2 class="text-info">Remove Event Location</h2></div>'+
                        '<div class="modal-body">'+
                        '<p>Are you sure you want to permanently remove this event location?</p>'+
                        '</div>' + 
                        '<div class="modal-footer"><button class="btn btn-danger" ng-click="remove()">Remove</button>'+
                        '<button class="btn btn-default" ng-click="$dismiss()">Cancel</button></div>',
                    controller: ['$scope', '$uibModalInstance', function($scope, $uibModalInstance) {
                        $scope.remove = function() {
                            EventSettingsService.removeLocation(eventId, id)
                            .success(function () {
                                toastr.success('Succesfully removed')
                                $uibModalInstance.close(true)
                            }).error(function (data) {
                                var msg = data.message || data.validation || data.error
                                if(msg) toastr.error(msg);
                                $uibModalInstance.dismiss()
                            })
                        };
                    }]
                }).result.then(function onClose (removed) {
                    if(removed) __loadLocations()
                });
            }
        },
        controller: function () {

        }
    }
}
