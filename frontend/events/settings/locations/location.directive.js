angular.module('SportWrench').directive('eventLocationForm', eventLocationForm);

eventLocationForm.$inject = ['EventSettingsService', 'geoService', 'toastr'];

function eventLocationForm(EventSettingsService, geoService, toastr) {
    return {
        restrict: 'E',
        scope: {
            id: '@',
            eventId: '@'
        },
        templateUrl: 'events/settings/locations/location.html',
        link: function (scope) {
            scope.location = {}
            scope.utils = {
                states: [],
                statesLoading: false,
                locationName: ''
            }

            scope.getTitle = function () {
                return (this.id)
                            ?'Update Location'
                            :'Create New Location'
            }

            scope.getSubtitle = function () {
                return this.utils.locationName;
            }

            scope.cancel = function () {
               this.$parent.$dismiss();
            }

            scope.submit = function () {
                this.utils.formSubmitted = true;
                
                if(scope.utils.locationForm.$invalid) {
                    return toastr.warning('Invalid values entered!')
                }

                ((scope.id)
                    ?__updateLocation()
                    :__createLocation()
                ).then(function () {
                    toastr.success('Successful ' + ((scope.id)?'Update':'Creation'))
                    scope.$parent.$close(true);
                }, function (resp) {
                    var data = resp && resp.data,
                        msg;
                    if(!data) return;
                    msg = (data.error || data.message || data.validation)
                    if(msg) toastr.error(msg);
                })
            }

            if(scope.id) {
                EventSettingsService.loadLocationInfo(scope.eventId, scope.id)
                .success(function (data) {
                    scope.location = data.location
                    scope.utils.locationName = scope.location.name;
                })
            } 

            __loadStates();

            function __loadStates() {
                scope.utils.statesLoading = true;
                geoService.getStates().then(function (states) {
                    scope.utils.states = states
                    scope.utils.statesLoading = false;
                })
            }

            function __updateLocation () {
                return EventSettingsService.updateLocation(scope.eventId, scope.id, scope.location);
            }

            function __createLocation () {
                return EventSettingsService.createLocation(scope.eventId, scope.location);
            }
        }
    }
}
