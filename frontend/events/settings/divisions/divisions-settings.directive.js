angular.module('SportWrench').directive('divisionsSettings', divisionsSettings);

divisionsSettings.$inject = ['EventSettingsService'];

function divisionsSettings(EventSettingsService) {
    return {
        restrict: 'E',
        scope: {},
        templateUrl: 'events/settings/divisions/divisions-settings.html',
        require: '^eventSettings',
        link: function (scope, attrs, elem, ctrl) {
            var mode = ctrl.getMode(),
                eventId = ctrl.getEventId();
            scope.divisions = [];
            scope.$watch(function () {
                return ctrl.isDivisionsActive();
            }, function (activate) {
                if(activate === true && mode === 'update') {
                    EventSettingsService.loadDivisions(eventId)
                }
            })
        }
    }
}
