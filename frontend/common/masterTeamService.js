angular.module('SportWrench').factory('masterTeamService', masterTeamService);

masterTeamService.$inject = ['$http'];

function masterTeamService($http) {
    var _create = function(obj) {
        return $http.post('/api/master_team', obj);
    };
    var _update  = function(obj, master_team_id) {
        return $http.put('/api/master_team/' + master_team_id, obj);
    };

    let teamAges = [{
        id: 0,
        title: 'Adult'
    }];

    for (let i = 8; i <= 18; ++i) {
        teamAges.push({
            id      : i,
            title   : i + ' & Under'
        });
    }

    return {
        update: function(obj, master_team_id,callBack) {
            _update(obj, master_team_id).then(function(resp) {
                callBack(resp);
            });
        },
        create: function(obj, callBack) {
            _create(obj).then(function(resp) {
                callBack(resp);
            });
        },
        remove: function (teamsList) {
            return $http.post('/api/club/teams/remove', { teams: teamsList });
        },
        count: function(params, callback) {
            $http.get('/api/master_team/count', {
                params: params
            }).then(function(res) {
                callback(res.data);
            });
        },
        // entered teams list
        findEnteredTeams: function (teams) {
            return $http.post('/api/club/teams/entered-list', { teams: teams });
        },
        upcomingEvents: function (teamId, params) {
            return $http.get('/api/club/teams/' + teamId + '/events/upcoming', { params: params });
        },
        getAges: function () {
            return teamAges;
        },
        getSportVariations: function () {
            return [
                { id: 1, title: 'Indoor' },
                { id: 3, title: 'Doubles' }
            ];
        },
        getRanks: function () {
            return Array.from({length: 99}, (_, i) => (i + 1).toString());
        },
    };
}
