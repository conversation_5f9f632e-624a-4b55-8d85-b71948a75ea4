angular.module('SportWrench').factory('doublesService', doublesService);

doublesService.$inject = ['$http'];

function doublesService($http) {

	var _getEventInfo = function(id) {
		return $http.get('/api/doubles/event/' + id + '/info');
	};

	var _register = function(event, division) {
		return $http.get('/api/doubles/event/' + event + '/division/' + division + '/info');
	};

	return  {
		getEventInfo: function(id, cbSuccess, cbError) {
			_getEventInfo(id).success(function(resp) {
				cbSuccess(resp);
			}).error(function(resp) {
				cbError(resp);
			});
		},
		register: function(event, division, cbSuccess) {
			_register(event, division).success(function(resp) {
				cbSuccess(resp);
			});
		},
	};
}
