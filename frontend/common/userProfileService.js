angular.module('SportWrench').factory('userProfileService', userProfileService);

userProfileService.$inject = ['$uibModal', 'APP_ROUTES', 'USER_ROLE', '$state'];

function userProfileService ($uibModal, APP_ROUTES, USER_ROLE, $state) {
    return {
        openEditProfileLink: function (role) {
            let state = (role === USER_ROLE.CD)
                ? APP_ROUTES.CD.INFO_UPDATE
                : APP_ROUTES.OF.INFO_UPDATE;

            $state.go(state);
        },

        openUserProfileCompletenessErrorModal: function (role) {
            let self = this;

            function __onClose () {
                self.openEditProfileLink(role);
            }

            return $uibModal.open({
                template: `
                    <modal-wrapper>
                        <profile-completeness-alert class="text-danger" role="role"></profile-completeness-alert>
                        <external-button
                            ng-class="geButtonClass()"
                            ng-click="openEditProfileLink()"
                            ng-bind="'Update Profile'"
                        ></external-button>
                    </modal-wrapper>`,
                controller: ['$scope', function ($scope) {
                    $scope.role = role;

                    $scope.geButtonClass        = function () {
                        return 'btn btn-primary pull-right';
                    };

                    $scope.modalShowClose       = false;

                    $scope.openEditProfileLink  = function () {
                        $scope.$close();
                    };
                }]
            }).result.then(__onClose, __onClose);
        }
    }
}
