angular.module('SportWrench').controller('SalesController', SalesController);

SalesController.$inject = ['$scope', '$rootScope', 'APP_ROUTES', 'userService', 'SalesService', 'eventDashboardService'];

function SalesController ($scope, $rootScope, APP_ROUTES, userService, SalesService, eventDashboardService) {
    $scope.tabs = [
        { name: 'Exhibitors',           state: APP_ROUTES.SM.EXHIBITORS,    isVisible: showExhibitorsTab },
        { name: 'Event Report',         state: APP_ROUTES.SM.REPORT,        isVisible: visible   },
        { name: 'Events',               state: APP_ROUTES.SM.EVENTS,        isVisible: visible   },
        { name: 'Invoices',             state: APP_ROUTES.SM.INVOICES,      isVisible: visible   },
        {
            name: () => $scope.event.name,
            states: [
                { name: 'Applications List', state: APP_ROUTES.SM.EVENT_EXHIBITORS },
                { name: 'Payments List', state: APP_ROUTES.SM.EVENT_EXHIBITORS_PAYMENTS },
                { name: 'My Exhibitors', state: APP_ROUTES.SM.EVENT_USER_EXHIBITORS },
                { name: 'Booths', state: APP_ROUTES.SM.EVENT_BOOTHS },
                { name: 'QR Codes', state: APP_ROUTES.SM.EVENT_EXHIBITORS_TICKETS },
            ],
            isVisible: visibleEventTab,
            stateParams : function () { return { event: $scope.event.event_id } }
        },
    ];

    function showExhibitorsTab() {
        return userService.isSales() || userService.hasGodRole();
    }

    function visible () {
        return true;
    }

    function visibleEventTab() {
        return typeof $scope.event === 'object';
    }

    $scope.$on('sales.event', async function (e, eventId) {
        if($scope.event && $scope.event.event_id === Number(eventId)) {
            return;
        }
        $scope.event = await SalesService.getEvent(eventId);
        eventDashboardService.setEvent($scope.event);
    });
}
