angular.module('SportWrench').controller('ReceiptsController', ReceiptsController);

ReceiptsController.$inject = ['$scope', '$http', '$stateParams', 'ExhibitorReceiptsService', 'ExhibitorsService'];

function ReceiptsController($scope, $http, $stateParams, ExhibitorReceiptsService, ExhibitorsService) {
    $scope.payments = [];
    $scope.exhibitors = [];
    $scope.events = [];
    $scope.filters = $stateParams.filters || {};

    function _fetch_data() {
        $http({
            url: '/api/sales/payments',
            method: 'GET'
        }).then(function(resp) {
            $scope.payments = resp.data.payments;
        });

        $http({
            method: 'GET',
            url: '/api/sales/sponsors?is_exhibitor=true'
        }).then(function(resp) {
            $scope.exhibitors = resp.data.sponsors;
        });

        $http({
            method: 'GET',
            url: '/api/sales/events/published/short'
        }).then(function(resp) {
            $scope.events = resp.data.events;
        });
    }

    _fetch_data(); 


    $scope.get_booths_payments = function(purchaseID) {
        return ExhibitorReceiptsService.getPaymentBooths(purchaseID);
    };

    $scope.receive_payment = function (purchase_id, date_paid, check_num, event_id) {
        return ExhibitorsService.makeReceive(purchase_id, { date_paid, check_num} , event_id);
    }   
}
