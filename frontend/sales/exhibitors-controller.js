angular.module('SportWrench').controller('ExhibitorsController', ExhibitorsController);

ExhibitorsController.$inject = ['$scope', '$http', '$state', '$window', 'loadData', 'APP_ROUTES'];

function ExhibitorsController ($scope, $http, $state, $window, loadData, APP_ROUTES) {
    $scope.sponsors     = loadData; 
    $scope.updateState  = APP_ROUTES.SM.EXHIBITORS_UPDATE;

    $scope.to_create_menu = function () {
        $state.go(APP_ROUTES.SM.EXHIBITORS_CREATE);
    }

    $scope.to_info_menu  = function (id) {
        $state.go(APP_ROUTES.SM.EXHIBITORS_INFO, {
            exhibitor: id
        });
    }

    $scope.xlsx_export = function () {
        window.location = '/api/sales/exhibitors/export';
    }

    $scope.orderColumn = 'last';
    $scope.reverseSort = false;

    $scope.orderData = function(colName) {
        $scope.orderColumn = colName;
        $scope.reverseSort = !$scope.reverseSort;
    };
}
