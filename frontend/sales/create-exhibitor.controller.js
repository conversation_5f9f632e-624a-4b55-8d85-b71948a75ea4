angular.module('SportWrench').controller('CreateExhibitorController', CreateExhibitorController);

CreateExhibitorController.$inject = [
    '$scope', '$http', '$rootScope', '$state', '$uibModalInstance', 'INTERNAL_ERROR_MSG', 'toastr', 'APP_ROUTES', 'INVALID_FORM_ERROR_MSG'
];

function CreateExhibitorController($scope, $http, $rootScope, $state, $uibModalInstance, INTERNAL_ERROR_MSG, toastr, APP_ROUTES, INVALID_FORM_ERROR_MSG) {
    var profileDefaults = {
        is_sponsor: false,
        is_exhibitor: false,
        is_non_profit: false,
        is_other: false,
        gender: 'male'
    };
    $scope.profile = angular.copy(profileDefaults);
    $scope.show_gender = true;
    $scope.creatingInProcess = false;

	$scope.emailPattern = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/;

    $scope.submit = function () {
        if ($scope.creatingInProcess) {
            return;
        }

        $scope.profileForm.$setSubmitted();

        if($scope.profileForm.$invalid) {
            toastr.warning(INVALID_FORM_ERROR_MSG);
            return;
        }

        $scope.creatingInProcess = true;
        $scope.error = null;
        $http.post('/api/sales/sponsor', $scope.profile)
        .success(function () {
            toastr.success('Successfully created');
            $uibModalInstance.close();
        }).error(function (data) {
            $scope.error = (data && data.validation) || INTERNAL_ERROR_MSG;
        })
        .finally(() => {
            $scope.creatingInProcess = false;
        });
    };
}
