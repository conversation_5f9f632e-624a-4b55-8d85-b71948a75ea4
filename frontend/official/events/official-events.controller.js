angular.module('SportWrench').controller('OfficialEventsController', OfficialEventsController);

OfficialEventsController.$inject = [
    '$scope', '$rootScope', '$state', 'officialService', 'APP_ROUTES', '_', '$filter', 'UtilsService', 'userProfileService', 'USER_ROLE'
];

function OfficialEventsController (
    $scope, $rootScope, $state, officialService, APP_ROUTES, _, $filter, UtilsService, userProfileService, USER_ROLE
) {
    var UTCDateFilter = $filter('UTCdate');

    $scope.utils 	= {
        loading: false,
        showOfficialStatusColumn: false,
        showStaffStatusColumn: false,
        isParticipationConfirmInProgress: false,
    };

    let official = $scope.$parent.official;

    $scope.states	= {
    	checkin : APP_ROUTES.OF.EVENTS_CHECKIN,
    	event 	: APP_ROUTES.OF.MANAGE_EVENT,
        info 	: APP_ROUTES.OF.EVENTS_INFO,
        payouts : APP_ROUTES.OF.PAYOUTS
    };

    let dateFormat  = 'MM/dd/yyyy';
    $scope.utils = {
        format      : dateFormat,
        placeholder : dateFormat.toLowerCase(),
        fromPicker  : {
            isOpen: false
        },
        toPicker    : {
            isOpen: false
        }
    };

    $scope.filters = {
        search: '',
        sanctioning_body: [],
        state: [],
        date_start_form: null,
        date_start_till: null
    }

    loadEvents();

    $scope.filterSearch = function () {
        loadEvents();
    }

    $scope.filterSanctioningBody = function (value) {
        $scope.filters.sanctioning_body = value;
        loadEvents();
    }

    $scope.filterState = function (value) {
        $scope.filters.state = value;
        loadEvents();
    }

    $scope.onDateChanged = function () {
        loadEvents();
    }

    $scope.goToEventCheckin = function (event) {
        if(official.not_completed) {
            return userProfileService.openUserProfileCompletenessErrorModal(USER_ROLE.OFFICIAL);
        } else {
            $state.go($scope.states.checkin, { event: event.event_id });
        }
    };

    $scope.showYear = function (event, index, eventsList) {
        return UtilsService.showYear(event, index, eventsList, true);
    };

    $scope.showPayoutsButton = function(event) {
        return event.is_official_registered && event.work_status == 'approved' && event.has_platform;
    };

    const checkRoles = ({ is_staff, is_official }) => {
        if (!$scope.utils.showOfficialStatusColumn) {
            $scope.utils.showOfficialStatusColumn = is_official;
        }
        
        if (!$scope.utils.showStaffStatusColumn) {
            $scope.utils.showStaffStatusColumn = is_staff;
        }
    }

    $scope.showParticipationConfirmButton = function(event) {
        return event.show_participation_confirm_button;
    }

    $scope.confirmParticipation = function(event) {
        if ($scope.utils.isParticipationConfirmInProgress)  {
            return;
        }

        $scope.utils.isParticipationConfirmInProgress = true;

        officialService.confirmOfficialParticipation(event.event_id)
            .then(() => {
                event.show_participation_confirm_button = false;
            })
            .finally(() => {
                $scope.utils.isParticipationConfirmInProgress = false;
            })
    }

    function loadEvents () {
        $scope.utils.loading = true;

        return officialService.officialRes().getEvent($scope.filters, function (response) {

            $scope.events = _.map(response.events, function (event) {
                checkRoles(event);

                event.date_start_form   = UTCDateFilter(event.date_start, 'MMM DD, YYYY');
                event.year              = +UTCDateFilter(event.date_start, 'YYYY');

                return event;
            });

            $scope.utils.loading 	= false;
        });
    }
}
