angular.module('SportWrench').controller('faqDetailsController', faqDetailsController);

faqDetailsController.$inject = ['$scope', '$sce', '$stateParams'];

function faqDetailsController($scope, $sce, $stateParams) {

	$scope.frameSource= 'api/faq/docs/' + $stateParams.faqPageName ;

	$("#faqFrame").on('load', ()=>{
        const css = `
            span {
                width: auto!important;
                height: auto!important;
            }
            html {
                overflow:hidden;
            }
            @media (max-width: 800px) {
                 body {
                    padding: 0 !important;
                    height: auto!important;
                }
                ol li {
                    margin-left: 10px!important;
                }
                p {
                    width: auto!important;
                    height: auto!important;
                }
                 img {
                    width: 100%!important;
                    height: auto!important;
                }
            }`;

        const style = document.createElement("style");
        style.textContent = css;
        document.head.appendChild(style);

        const offsetHeight = document.querySelector(".container-fluid.block-height-min").offsetHeight;
        $("#faqFrame").css( 'height', offsetHeight + 30);
	})
}
