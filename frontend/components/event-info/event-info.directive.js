angular.module('SportWrench').directive('eventInfo', eventInfo);

eventInfo.$inject = ['$state', '$window', 'APP_ROUTES', 'SWT_URL'];

function eventInfo($state, $window, APP_ROUTES, SWT_URL) {
    return {
        restrict: 'E',
        scope: {
            info: '='
        },
        templateUrl: 'components/event-info/event-info.html',
        link: function (scope) {
            scope.hasSocialLinks = function () {
                return !(this.info && _.isEmpty(this.info.social_links));
            }

            scope.openDoublesRegForm = function () {
                if(!(scope.info && scope.info.event_id)) return;
                $state.go(APP_ROUTES.D.TYPE, {
                    event: scope.info.event_id
                })
            }

            scope.isDoubles = function () {
                return this.info && (this.info.registration_method === 'doubles')
            }

            scope.hasTeams = function () {
                return this.info && (this.info.allow_teams_registration)
            }

            scope.showSWTDate = function () {
                return this.info && (this.info.tickets_published && !this.info.tickets_code)
            }

            scope.showSWTLink = function () {
                return this.info && (this.info.tickets_code)
            }

            scope.openSWT = function () {
                $window.location.replace(SWT_URL + '/#/events/' + this.info.tickets_code)
            }

            scope.locationsExists = function () {
                let info = this.info;
                return info && info.locations && info.locations.length;
            }
            
            scope.getLocationTitle = function (locationNumber) {
                return this.info && this.info.locations.length > 1
                    ? `Event Location #${locationNumber}:`
                    : 'Event Location:';
            }
        }
    }
}
