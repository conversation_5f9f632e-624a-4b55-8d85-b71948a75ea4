angular.module('SportWrench').directive('housingNotes', housingNotes);

housingNotes.$inject = ['$http', '$stateParams', '$timeout'];

function housingNotes($http, $stateParams, $timeout) {
    return {
        restrict: 'E',
        scope: {
            team: '='
        },
        templateUrl: 'components/housing-notes/housing-notes.html',
        link: function (scope) {
            scope.hideAddNoteField = true;
            scope.notes = [];
            scope.edit = [];
            scope.new_event_note = {
                comments: '',
                action: 'team.housing.note'
            };

            __loadNotes();

            scope.$on('notes.updated', () => {
                __loadNotes();
            });

            scope.addNote = function (obj) {
                if (!obj.action || !obj.comments) return;
                obj.roster_club_id  = scope.team.roster_club_id;

                return __addNote(obj);
            };

            scope.getNoteTypeText = function (action) {
                return /club/.test(action) ? 'Club note: ':'Team note: ';
            };

            scope.editNote = function (note) {
                scope.edit[note] = !scope.edit[note];
            };

            scope.saveEditedNote = function (note, comment) {
                if (comment) {
                    scope.notes[note].comments = scope.notes[note].comments + "\n" + comment;

                    __saveNote(scope.notes[note].event_change_id, scope.notes[note]);
                }

                scope.edit[note] = !scope.edit[note];
            };

            function __loadNotes() {
                $http.get('/api/housing/events/'+$stateParams.event+'/teams/'+scope.team.roster_team_id+'/notes')
                    .success(function (data) {
                        scope.notes = data;
                    });
            };

            function __addNote(obj) {
                $http.post('/api/housing/events/'+$stateParams.event+'/teams/'+scope.team.roster_team_id+'/notes', obj)
                    .then(function () {
                        scope.new_event_note.comments = '';
                        __loadNotes();
                    });
            };

            function __saveNote(noteId, obj) {
                $http.put('/api/housing/note/'+noteId+'/update', obj)
                    .then(function () {
                        __loadNotes();
                    });
            };
        }

    }
}
