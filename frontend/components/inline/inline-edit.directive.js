angular.module('SportWrench')
.directive('editableGrid', editableGrid)
.directive('inlineEdit', inlineEdit)
.directive('rawValue', rawValue);

editableGrid.$inject = [];
inlineEdit.$inject = [];
rawValue.$inject = [];

function editableGrid() {
    return {
        restrict: 'EA',
        scope: true,
        link: function (scope, elem, attrs) {
            scope.oneActive = attrs.oneActive;
        },
        controller: ['$scope', function ($scope) {
            this.closeOther = function () {
                if(!!$scope.oneActive) {
                    $scope.$broadcast('inline-edit.close')
                }
            }
        }]
    }
}

function inlineEdit() {
    return {
        restrict: 'E',
        scope: {
            editValue : '@',
            onToggle  : '&',
            blockEdit : '&?'
        },
        templateUrl     : 'components/inline/inline-edit.html',
        transclude      : true,
        require         : '^editableGrid',
        link: function (scope, elem, attrs, ctrl, transclude) {
            var editableContentNode = elem.find('span.editable');
            scope.showEdit      = false;
            scope.leaveActive   = true;

            scope.toggleEdit = function () {
                if(scope.blockEdit && scope.blockEdit() && !scope.showEdit)
                    return;
                scope.showEdit = !scope.showEdit;
                scope.onToggle({ isEdit: scope.showEdit });
                if(scope.showEdit) {
                    scope.leaveActive = true;
                    ctrl.closeOther();
                }
            }

            scope.$on('inline-edit.close', function () {
                if(scope.showEdit) {
                    if(!scope.leaveActive) {
                        scope.toggleEdit();
                    } else {
                        scope.leaveActive = false;
                    }
                }
            })

            transclude(scope.$parent, function (contents) {
                angular.forEach(contents, function (domNode) {
                    var name;
                    if(domNode && domNode.tagName) {
                        name = domNode.tagName.toLowerCase();
                        switch(name) {
                            case 'edit-value':
                            case 'data-edit-value':
                            case 'x-edit-value':
                                scope.rawEditableContent = domNode;
                                break;
                            default:
                                editableContentNode.append(domNode);
                                break;
                        }
                    }
                })
            })
        },
        controller: [function () {}]
    }
}

function rawValue() {
    return {
        restrict    : 'AE',
        require     : '^inlineEdit',
        templateUrl : 'components/inline/inline-edit-raw.html',
        link: function (scope, elem) {
            scope.$watch('rawEditableContent', function (v) {
                if(v === undefined) return;
                elem.html('')
                elem.append(v)
            })
        }
    }
}
