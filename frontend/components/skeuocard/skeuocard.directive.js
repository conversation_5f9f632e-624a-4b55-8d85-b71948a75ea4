angular.module('SportWrench').directive('skeuocard', skeuocard);

skeuocard.$inject = ['$timeout'];

function skeuocard($timeout) {
	return {
		restrict: 'E',
        scope: {
            card: '='
        },
		templateUrl: 'components/skeuocard/skeuocard.html',
		link: function (scope, elem, attrs) {

            $timeout(function () {
                var cardInstance = new Skeuocard($(elem).find('#skeuocard'));

                cardInstance.isValid = function () {
                  return !this.el.front.hasClass('invalid')
                    && (!this.el.back.hasClass('invalid') || !this._inputViewsByFace.back.length);
                };

                cardInstance.getData = function () {
                    return {
                        number       : angular.element('#cc_number').val(),
                        exp_month    : angular.element('#cc_exp_month').val(),
                        exp_year     : angular.element('#cc_exp_year').val(),
                        name         : angular.element('#cc_name').val(),
                        cvc          : angular.element('#cc_cvc').val()
                    };
                };

                if(attrs.card) {
                    scope.card = cardInstance;
                }
            });
		},

	};
}
