angular.module('SportWrench').directive('socialIcon', socialIcon);

socialIcon.$inject = ['SOCIAL_PATH', 'SOCIAL_NETWORKS'];

function socialIcon(SOCIAL_PATH, SOCIAL_NETWORKS) {
    return {
        restrict: 'E',
        scope: {
            key: '@',
            link: '@'
        },
        templateUrl: 'components/social-items/item.html',
        link: function (scope) {
            scope.networks = SOCIAL_NETWORKS;
            scope.img_path = SOCIAL_PATH;

            scope.exists = function () {
                return (!_.isEmpty(this.networks[this.key]) && this.link )
            }

            scope.isLink = function () {
                return (this.networks[this.key].type === 'url')
            }
        }
    }
}
