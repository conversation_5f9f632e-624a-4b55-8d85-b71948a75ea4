angular.module('SportWrench').directive('teamHistory', function () {
    return {
        restrict: 'E',
        scope: {
            history: '=',
            load: '&'
        },
        templateUrl: function (elem, attr) {
            return attr.isPopup ? 
                'components/team-history/team-history-popup.html' : 
                'components/team-history/team-history.html';
        },
        controller: ['$scope', '$uibModal', 'eventsService', 'AEMService', '$stateParams', 'AEMEventFactory',
            function ($scope, $uibModal, eventsService, AEMService, $stateParams, AEMEventFactory) {
            $scope.limit = 1;
            $scope.utils = {};
            $scope.filters = {};

            function load(filters) {
                const _filters = angular.copy(filters);

                if (_filters.from) {
                    _filters.from = moment(_filters.from).format('MM/DD/YYYY');
                }

                if (_filters.to) {
                    _filters.to = moment(_filters.to).format('MM/DD/YYYY');
                }

                $scope.load({ query: _filters });
            }

            $scope.$watch('filters', function (newVal, oldVal) {
                if (_.isEqual(newVal, oldVal)) {
                    return;
                }

                load($scope.filters);
            }, true);

            $scope.openModal = function (h) {
                if(h.event_email_id) {
                    let link;

                    if(h.email_template_id) {
                        let AEMEventService = new AEMEventFactory($stateParams.event);
                        link                = AEMEventService.getPreviewLink(h.email_template_id);

                        AEMService.openPreviewModal(null, link, null, null, null, h.action);
                    } else {
                        let link = eventsService.getEmailPreviewLink($stateParams.event, h.event_email_id);

                        let emailPreviewDataSource = eventsService.email_info.bind(eventsService, {
                            id: h.event_change_id, email_id: h.event_email_id
                        });

                        AEMService.openPreviewModal(null, link, null, emailPreviewDataSource);
                    }
                }
            };

            $scope.isEmailAction = function (h) {
                return h.event_email_id;
            };

        	$scope.showEmail = function(id, event_email_id) {
        		var modalInstance = $uibModal.open({
        			templateUrl: 'components/team-history/team-history-email.html',
        			controller: ['$scope', '$uibModalInstance', 'eventsService', function ($scope, $uibModalInstance, eventsService) {
                        $scope.email = {};

                        var data = {
                            id: id,
                            email_id: event_email_id
                        }

                        eventsService.email_info(data).then(data => {
                            $scope.email = data;
                        });

                        $scope.cancel = function() {
                            $uibModalInstance.dismiss();
                        };

                        $uibModalInstance.result.finally(function() {
                            $uibModalInstance.dismiss();
                        });
                    }]
        		});
        	};

            $scope.search_keypress = function (e) {
                if(e.charCode === 13) {
                    $scope.filters.search = $scope.utils.search;            
                }
            }

            $scope.toggleFrom = function () {
                $scope.utils.from_opened = !$scope.utils.from_opened
            }

            $scope.toggleTo = function () {
                $scope.utils.to_opened = !$scope.utils.to_opened;
            }
        }]
    }
})
