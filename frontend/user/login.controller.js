angular.module('SportWrench')

.config(function($uibTooltipProvider) {
    $uibTooltipProvider.setTriggers({
        'showError': 'hideError'
    });
})

.controller('loginController', loginController);

loginController.$inject = ['$scope', '$http', '$window', '$uibModal', '$state', 'loginMessageService', 'userService', 'APP_ROUTES'];

function loginController(
    $scope, $http, $window, $uibModal, $state, loginMessageService, userService, APP_ROUTES
) {
    $scope.formData = {};
    $scope.utils = { message: loginMessageService.getMessage() };
    $scope.processLoginForm = function() {
        $http.post('/api/signin', $scope.formData)
        .success(function(res) {         
            userService.logIn(res.data.user);

            $scope.showName();

            if(userService.isEventOwner()) {
                $state.go(APP_ROUTES.EO.EVENTS);
            } else if(userService.isClubDirector()) {
                $state.go(APP_ROUTES.CD.INFO);
            } else if(userService.isSales()) {
                $state.go(APP_ROUTES.SM.EVENTS);
            } else if(userService.isSponsor()) {
                $state.go(APP_ROUTES.EX.PROFILE);
            } else if(userService.isStaff()) {
                $state.go(APP_ROUTES.OF.INFO);
            } else if (userService.isHousingManager()) {
                $state.go(APP_ROUTES.HOUSING_EVENTS)
            } else if (userService.hasTickets()) {
                $state.go(APP_ROUTES.TICKETS_LIST)
            } else if (userService.isUSAVAdmin()) {
                $state.go(APP_ROUTES.UA.EVENTS)
            } else {
                $state.go(APP_ROUTES.INDEX, {
                    accord: 'spectators'
                });
            }
        }).error(function (data) {
            if(data) {
                $scope.utils.message = (data.error)
                                ?data.error
                                :(data.validationErrors && data.validationErrors.length)
                                    ?data.validationErrors[0].msg
                                    :null

                if ($scope.utils.message === 'Incorrect username') {
                    $scope.loginForm.email.$setValidity('username', false);
                } else if ($scope.utils.message === 'Incorrect password') {
                    $scope.loginForm.password.$setValidity('password', false);
                }
            }            
        });
    };

    $scope.recover = function() {
        $state.go(APP_ROUTES.FORGOT_PSWD);
    };
}
