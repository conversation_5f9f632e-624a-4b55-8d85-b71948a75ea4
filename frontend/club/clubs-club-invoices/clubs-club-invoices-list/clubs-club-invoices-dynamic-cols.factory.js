angular.module('SportWrench').factory('ClubsClubInvoicesDynamicColsFactory', ClubsClubInvoicesDynamicColsFactory);

ClubsClubInvoicesDynamicColsFactory.$inject = ['NG_TABLE_TOOLTIPS'];

function ClubsClubInvoicesDynamicColsFactory(NG_TABLE_TOOLTIPS) {
    return {
        getColumns: function () {
            return [
                {
                    title           : 'Created',
                    name            : 'created',
                    sortable        : 'created',
                    visible         : true
                }, {
                    title           : 'Amount',
                    name            : 'amount',
                    sortable        : 'amount',
                    visible         : true
                }, {
                    title           : 'Event Name',
                    name            : 'event_name',
                    sortable        : 'event_name',
                    visible         : true
                }, {
                    title           : 'Paid',
                    name            : 'paid',
                    sortable        : 'paid',
                    visible         : true
                }, {
                    title           : 'Status',
                    name            : 'status',
                    sortable        : 'status',
                    tooltip         : NG_TABLE_TOOLTIPS.REFUNDED,
                    visible         : true
                }, {
                    title           : 'Type',
                    name            : 'type',
                    sortable        : 'type',
                    visible         : true
                }
            ];
        }
    };
}
