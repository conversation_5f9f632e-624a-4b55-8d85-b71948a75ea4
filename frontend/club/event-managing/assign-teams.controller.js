angular.module('SportWrench').controller('Club.Event.AssignTeamsController', AssignTeamsController);

AssignTeamsController.$inject = [
    '$scope', '$stateParams', 'masterTeamService', '$state', 'ClubTeamsService',
    '$filter', '$rootScope', '$window', '$http', 'INTERNAL_ERROR_MSG', 'toastr',
    'ClubCheckinService', 'APP_ROUTES', '$uibModal', 'CUSTOM_FORM_TYPE', 'loadClub',
    'verticalInsuranceService', 'TEAM_STATUS'
];

function AssignTeamsController (
    $scope, $stateParams, masterTeamService, $state, ClubTeamsService, 
    $filter, $rootScope, $window, $http, INTERNAL_ERROR_MSG, toastr,
    ClubCheckinService, APP_ROUTES, $uibModal, CUSTOM_FORM_TYPE, loadClub,
    verticalInsuranceService, TEAM_STATUS
) {
    $scope.teams = [];
    $scope.search = {
        value       : '',
        age         : {},
        rank        : {},
        show_teams  : 'any'
    };
    $scope.loaded = false;
    $scope.utils = {
        validationProccess: {},
        errorMessage: '',
    };

    _loadTeams();    

    $scope.$on('club.entry.paid', function () {
        _loadTeams();
    });

    $scope.$on('club.entry.payment.failed', function () {
        _loadTeams();
    });

    function _loadTeams () {
        $http.get('/api/club/teams/' + $stateParams.event + '/list')
        .success(function (data) {
            $scope.teams = data.teams;
            $scope.loaded = true;
        });
    }

    $scope.showVIProposalButton = function () {
        const hasPaidTeams = $scope.teams.some(team => {
            return team.status_paid === TEAM_STATUS.PAYMENT.PAID
        });
        const VIModeEnabled = $scope.event.allow_vertical_insurance;
        const earlierDayBeforeStartEvent = $scope.event.earlier_day_before_start_event;

        return hasPaidTeams && VIModeEnabled && earlierDayBeforeStartEvent;
    }

    $scope.getTeamAgeLabel = function (age) {
        return Number(age) === 0 ? 'Adult' : age;
    }

    $scope.redirectToVIProposalPage = function () {
        return verticalInsuranceService.redirectToVIProposalPage($stateParams.event);
    }

    function openCustomFormModal (formType, customFormEventID) {
        const eventID = Number($scope.event.event_id);

        return $uibModal.open({
            template : `<modal-wrapper>
                            <div class="modal-body">
                                <custom-form 
                                    event-id="event" 
                                    custom-form-event-id="customFormEventID" 
                                    type="formType"
                                    submitter="submitter" 
                                    on-cancel="close(submitted)"/>
                            </div>
                        </modal-wrapper>`,
            size: 'md',
            controller: ['$scope', '$uibModalInstance', function ($scope, $uibModalInstance) {
                $scope.event = eventID;
                $scope.customFormEventID = customFormEventID;
                $scope.submitter = loadClub.master_club_id;
                $scope.formType = formType;

                $scope.close = function (submitted) {
                    $uibModalInstance.close(submitted);
                }
            }]
        }).result.then((submitted) => {
            return submitted;
        }, () => {
            return false;
        })
    }

    $scope.divisionChange = async function (prop, val, item, done) {
        clearErrorMessage();

        if(val && val > 0) {
            const customFormsRequiresSubmitting = $scope.event.custom_forms_requires_submitting;

            if(!_.isEmpty(customFormsRequiresSubmitting)) {
                for(const { custom_form_event_id, type } of customFormsRequiresSubmitting) {
                    if(type === CUSTOM_FORM_TYPE.TEAM_ASSIGN_FOR_EVENT) {
                        const success = await openCustomFormModal(type, custom_form_event_id);

                        if(success) {
                            $scope.event.custom_forms_requires_submitting = customFormsRequiresSubmitting.filter(
                                (form) => form.custom_form_event_id !== custom_form_event_id
                            )
                        }

                        if(!success) {
                            $scope.utils.entryChangeInProgress = false;
                            return done({ failed: true });
                        }
                    }
                }

            }
        }

        if(item.roster_team_id) {
            $scope.utils.entryChangeInProgress = true;
            ClubTeamsService.roster.updateTeam($stateParams.event, {
                division        : val,
                roster_club     : $scope.event.roster_club,
                team            : item.roster_team_id
            }).success(function (data) {
                $scope.utils.entryChangeInProgress = false;
                if(!val || val < 0) {
                    toastr.success('Team has been successfully removed from event');
                    return done({ division_id: -1 });
                } else {
                    toastr.success('Team has been successfully moved to another division.');                    
                    return done({
                        division_id     : val,
                        division_name   : data.division.name,
                        status_paid     : data.team.status_paid,
                        status_entry    : data.team.status_entry
                    });
                }
            }).error(function (data) {
                $scope.reloadDivisions();
                $scope.utils.entryChangeInProgress = false;
                return done({
                    failed: true,
                });
            });
        } else {
            if(!val || val < 0) {
                $scope.utils.entryChangeInProgress = false;
                return done();
            }
            $scope.utils.entryChangeInProgress = true;
            ClubTeamsService.roster.assignTeam($stateParams.event, {
                roster_club     : $scope.event.roster_club,
                team            : item.master_team_id,
                division        : val
            }).success(function (data) {
                for (var i = 0, l = $scope.teams.length; i < l; ++i) {
                    if (item.master_team_id === $scope.teams[i].master_team_id) {
                        $scope.teams[i].roster_team_id = data.team.roster_team_id;
                    }
                }

                if(!$scope.$parent.event.roster_club) {
                    $scope.$parent.event.roster_club = data.team.roster_club_id
                }

                toastr.success('Team "' + data.team.team_name + '" has been successfully entered to event');

                $scope.utils.entryChangeInProgress = false;

                return done({
                    division_id: val,
                    status_paid: data.team.status_paid,
                    status_entry: data.team.status_entry
                });
            }).error(function (error) {
                handleError(error);

                $scope.utils.entryChangeInProgress = false;
                $scope.reloadDivisions();
                item.division_id = -1;
                return done({
                    failed: true,
                });
            })
        }
    }

    $scope.paymentMenu = function () {
        if ($scope.event.teams_entry_sw_fee === null) {
            return;
        }

        $state.go(APP_ROUTES.CD.MANAGE_EVENT_TEAMS_PAY);
    };

    $scope.teamsOrder = function (team) {
        return (team.roster_team_id)
                    ?(team.deleted)?1:0
                    :2
    };

    $scope.ageFilter = function() {
        return function (team) {
            var flag = true;
            if ($scope.search.age.from) {
                if (team.age < +$scope.search.age.from) {
                    flag = false;
                }
            }
            if ($scope.search.age.to) {
                if (team.age > +$scope.search.age.to) {
                    flag = false;
                }
            }         
            return flag;
        }
    };

    $scope.rankFilter = function() {
        return function(team) {
            var flag = true;
            if ($scope.search.rank.from) {
                if (team.rank < +$scope.search.rank.from) {
                    flag = false;
                }
            }
            if ($scope.search.rank.to) {
                if (team.rank > +$scope.search.rank.to) {
                    flag = false;
                }
            }
            return flag;
        }
    };

    $scope.genderFilter = function() {
        return function (team) {
            var flag = false;
            if(team.gender === 'male' && $scope.genders.male) {
                flag = true;
            } 
            if(team.gender === 'female' && $scope.genders.female) {
                flag = true;
            }
            if(team.gender === 'coed' && $scope.genders.coed) {
                flag = true;
            }
            return flag;
        }
    };

    $scope.printButtonText = function () {
        return $scope.utils.printAllValidation?'Validating ...':'Print All Rosters'
    }

    $scope.printAllRosters = function () {
        var teams        = [],
            disableBlock = function () {
                $scope.utils.entryChangeInProgress = false;
                $scope.utils.printAllValidation = false;
            }
        $scope.teams.forEach(function (t) {
            if(t.roster_team_id && (t.status_entry !== 11)) {
                teams.push(t.roster_team_id)
            }
        });
        if(!teams.length) {
            toastr.warning('Not Teams Entered the Event');
            return;
        }
        // to disable all the page's controls
        $scope.utils.entryChangeInProgress = true;
        $scope.utils.printAllValidation = true;

        ClubCheckinService.validateTeams($stateParams.event, teams, function (id) {
            for(var i = 0, l = $scope.teams.length; i < l; ++i) {
                if($scope.teams[i].roster_team_id === id)
                    return $scope.teams[i];
            }
        }).then(function (rosterValidationErrors) {
            if(rosterValidationErrors && rosterValidationErrors.length) {
                return ClubCheckinService.openValidationErrorsModal(rosterValidationErrors)
            }
        }).then(function () {
            ClubTeamsService.roster.showCheckInList($stateParams.event);
        }).then(disableBlock, disableBlock)
    }

    $scope.showCheckInList = function (team) {
        var rosterTeamId = team.roster_team_id,
            disableSpinner = function () {
            $scope.utils.validationProccess[rosterTeamId] = false;
        }
        $scope.utils.validationProccess[rosterTeamId] = true;
        ClubTeamsService.roster.validateRoster($stateParams.event, rosterTeamId)
        .then(function (resp) {
            var validationResult = resp && resp.data.checkinValidation;
            if(_.isEmpty(validationResult)) {
                ClubTeamsService.roster.showCheckInList($stateParams.event, rosterTeamId)
            } else {
                return ClubTeamsService.roster.validationModal(
                    validationResult, {
                        team        : team,
                        event_id    : $stateParams.event,
                        showLink    : true
                    }
                )
            }
        }).then(disableSpinner, disableSpinner)
    }

    $scope.showEnteredTeamsFilter = function() {
        return function (team) {
            if($scope.search.show_teams === 'any') {
                return true;
            } else if($scope.search.show_teams === 'entered' && team.roster_team_id) {
                return true;
            } else if($scope.search.show_teams === 'not_entered' && !team.roster_team_id) {
                return true
            }
            return false;
        }
    }

    $scope.showAthletes = function (t) {
        if(!t || !t.roster_team_id || t.deleted || t.status_entry === 11) return;
        $state.go(APP_ROUTES.CD.MANAGE_EVENT_MEMBERS, {
            team    : t.roster_team_id,
            event   : $stateParams.event
        })
    }

    $scope.exportResults = function (team_id) {
        window.location = '/api/club/event/' + $stateParams.event + '/results?team=' + team_id;
    }

    $scope.exportResultsAES = function (team_id) {
        window.location = '/api/club/event/' + $stateParams.event + '/results?team=' + team_id + 
                                '&format=csv';
    }

    $scope.exportResultsTM2 = function (team_id) {
        window.location = '/api/club/event/' + $stateParams.event + '/results?team=' + team_id + 
                                '&format=xls';
    }

    $scope.disablePrintBtn = function () {
        return !(this.$parent.event && this.$parent.event.roster_club);
    }

    $scope.showDivisionsSelect = function (team) {
        return team.status_paid != 24 && team.status_paid != 22
                && team.status_entry != 11 && team.status_entry != 12 && !$scope.event.past;
    }

    function handleError(error) {
        $scope.utils.errorMessage = error && error.validation
            ? error.validation
            : INTERNAL_ERROR_MSG
    }

    function clearErrorMessage() {
        if (!$scope.utils.errorMessage) {
            return;
        }

        $scope.utils.errorMessage = '';
    }
}
