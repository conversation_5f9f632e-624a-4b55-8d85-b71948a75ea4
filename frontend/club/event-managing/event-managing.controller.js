angular.module('SportWrench').controller('Club.Event.ManagingController', EventManagingController);

EventManagingController.$inject = ['$scope', 'eventsService', '$stateParams', '$state', '$rootScope', 'eventData', 'APP_ROUTES', '_'];

function EventManagingController($scope, eventsService, $stateParams, $state, $rootScope, eventData, APP_ROUTES, _) {
    $scope.active_tab = 2;
    $scope.current_state_name = 'event-info';
    $scope.master_club = {};
    $scope.event = deleteClosedDivisions(eventData);

    function deleteClosedDivisions(event) {
        event.divisions = _.filter(event.divisions, function (division) {
            if(!division.closed) return division;
        });
        return event;
    }

    $scope.event.divisions.push({ id: -1, name: 'Not entered' }); 

    $scope.genders = {
        male        : true,
        female      : true,
        coed        : true
    };

    $scope.athletes_opts = {};

    $scope.tabs = [
        {
            name        : 'Event info',
            state       : APP_ROUTES.CD.MANAGE_EVENT_INFO,
            isVisible   : __visible
        }, {
            name        : 'Teams Entry',
            state       : APP_ROUTES.CD.MANAGE_EVENT_TEAMS,
            isVisible   : __visible
        }, {
            name        : 'Event Roster',
            state       : APP_ROUTES.CD.MANAGE_EVENT_MEMBERS,
            isVisible   : __visible
        }, {
            name        : 'Online Team Check In',
            state       : APP_ROUTES.CD.MANAGE_EVENT_CHECKIN,
            isVisible   : __checkinAvailable
        }
    ];

    function __visible () {
        return true;
    }

    function __checkinAvailable () {
        return $scope.event.online_team_checkin_available && !$scope.event.past;
    }

    $rootScope.$broadcast('QUICK_MANAGING_TAB', {
        event_id    : $scope.event.event_id,
        name        : $scope.event.long_name
    });

    $scope.eventFinished = function () {
        return this.event.past
    }

    $scope.reloadDivisions = function () {
        eventsService.getEventDivisions($stateParams.event, function(data) {
            $scope.event.divisions = data.divisions;
            $scope.event.divisions.push({ id: -1, name: 'Not entered' });
        });     
    }
}
