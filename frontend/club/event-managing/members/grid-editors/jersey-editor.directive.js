angular.module('SportWrench').directive('jerseyEditor', jerseyEditor);

jerseyEditor.$inject = ['EventMembersService', 'toastr', 'OVERWRITE_VALUE_MODE', 'DEFAULT_VALUE_MODE'];

function jerseyEditor (EventMembersService, toastr, OVERWRITE_VALUE_MODE, DEFAULT_VALUE_MODE) {
	return {
		restrict: 'E',
		scope: {
			m 				: '=member',
			eventId 		: '@',
			onSaved 		: '&?',
			onAddonClick 	: '&',
			block 			: '='
		},
		templateUrl: 'club/event-managing/members/grid-editors/jersey-editor.html',
		link: function (scope) {
			scope.inlineJerseyEdit = {};

			scope.isOverwritten = function () {
				return scope.m.jersey && (scope.m.jersey !== scope.m.default_jersey)
			}

			scope.jerseyEditToggle = function (isEditMode, member) {
		        if(isEditMode) {
		        	if(scope.isOverwritten()) {
		        		scope.inlineJerseyEdit.value = member.jersey;
		        		scope.inlineJerseyEdit.type = OVERWRITE_VALUE_MODE;
		        	} else {
		        		scope.inlineJerseyEdit.value = member.default_jersey;
		        		scope.inlineJerseyEdit.type = DEFAULT_VALUE_MODE;
		        	}
		        } else {
		            scope.inlineJerseyEdit.value = undefined;
		            scope.inlineJerseyEdit.type = '';
		        }
		    }

		    scope.saveJerseyChange = function (m) {
		    	if(scope.inProgress) return;
		    	scope.inProgress = true;
		        var body 			= {}, 
		            data 			= scope.inlineJerseyEdit,
		            jersey 			        = data.value,
		            isRosterUpdates = (data.type === OVERWRITE_VALUE_MODE);

		        if(isRosterUpdates && jersey === scope.m.default_jersey) {
		        	body[data.type] = {
			            jersey: null
			        }
		        } else {
		        	body[data.type] = {
			            jersey: jersey
			        }
		        }

                if(!_.isEmpty(body.d)) {
                    if(!_.isNull(body.d.jersey) && body.d.jersey <= 0) {
                        toastr.warning('Uniform must be a positive number');
                        scope.inProgress = false;
                        return;
                    }
                }

                if(!_.isEmpty(body.o)) {
                    if(!_.isNull(body.o.jersey) && body.o.jersey <= 0) {
                        toastr.warning('Uniform must be a positive number');
                        scope.inProgress = false;
                        return;
                    }
                }

		        EventMembersService.updateAthlete(m.id, scope.eventId, body)
		        .then(function () {
		            if(isRosterUpdates) {
		                m.jersey = body[OVERWRITE_VALUE_MODE].jersey
		            } else {
		                m.default_jersey = body[DEFAULT_VALUE_MODE].jersey
		            }
		            scope.$broadcast('inline-edit.close');
		            if(scope.onSaved) scope.onSaved();
		        }, function (resp) {
		            if(resp && resp.data.validation) {
		                toastr.error(resp.data.validation)
		            }
		        }).then(function () {
		        	scope.inProgress = false;
		        })
		    }

		    scope.onEditKeypress = function ($event, m) {
		        if($event.charCode === 13) {
		            scope.saveJerseyChange(m);
		        }
			}

			scope.getDefaultJersey = function () {
				return (scope.isOverwritten())
							?('(' + scope.m.default_jersey + ')')
							:scope.m.default_jersey
			}
		}
	}
}
