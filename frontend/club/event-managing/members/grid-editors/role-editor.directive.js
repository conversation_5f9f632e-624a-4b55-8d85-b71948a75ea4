angular.module('SportWrench').directive('roleEditor', roleEditor);

roleEditor.$inject = ['STAFF_ROLES', 'EventMembersService', 'toastr', '$timeout', 'OVERWRITE_VALUE_MODE', 'DEFAULT_VALUE_MODE'];

function roleEditor (STAFF_ROLES, EventMembersService, toastr, $timeout, OVERWRITE_VALUE_MODE, DEFAULT_VALUE_MODE) {
	return {
		restrict: 'E',
		scope: {
			m 				: '=member',
			eventId 		: '@',
			onSaved 		: '&?',
			onAddonClick 	: '&',
			block 			: '='
		},
		templateUrl: 'club/event-managing/members/grid-editors/role-editor.html',
		link: function (scope) {
			scope.roles 			= STAFF_ROLES;
			scope.inlineRoleEdit 	= {};

			scope.isOverwritten 	= function () {
				return scope.m.role_id && (scope.m.role_id !== scope.m.default_role_id)
			};
            scope.isEqualDefaultAndOptionalRoles = function () {
                return scope.m.role_id === scope.m.default_role_id;
            }
			scope.roleEditToggle 	= function (isEditMode) {
				if(isEditMode) {
					if(scope.isOverwritten() || scope.isEqualDefaultAndOptionalRoles()) {
						scope.inlineRoleEdit.value 	= scope.m.role_id;
						scope.inlineRoleEdit.type 	= OVERWRITE_VALUE_MODE;
						scope.inlineRoleEdit.name 	= scope.m.role_name;
					} else {
						scope.inlineRoleEdit.value 	= scope.m.default_role_id;
						scope.inlineRoleEdit.type 	= DEFAULT_VALUE_MODE;
						scope.inlineRoleEdit.name 	= scope.m.default_role_name;
					}
				} else {
					scope.inlineRoleEdit.value 	= undefined;
					scope.inlineRoleEdit.type 	= '';
					scope.inlineRoleEdit.name 	= '';
				}
			};
			scope.saveRoleChange 	= function (choosenRole) {
				if(scope.inProgress) return;
				scope.inProgress = true;

				scope.inlineRoleEdit.name = choosenRole.short_name;

				var body = {};

                body[scope.inlineRoleEdit.type] = {
                    role_id: choosenRole.id
                }

				EventMembersService.updateStaff(scope.m.id, scope.eventId, body)
				.then(function (resp) {
					var respData 	= resp.data,
						member 		= scope.m;

					if(scope.isOverwritten() || scope.isEqualDefaultAndOptionalRoles()) {
						member.role_id 		= body[OVERWRITE_VALUE_MODE].role_id;
						member.role_name 	= formatRoleName(respData.o.role_name);
					} else {
						member.default_role_id 		= body[DEFAULT_VALUE_MODE].role_id;
						member.default_role_name 	= formatRoleName(respData.d.role_name);
					}

					scope.$broadcast('inline-edit.close');

					if(scope.onSaved) scope.onSaved();
				}, function (resp) {
					if(resp && resp.data.validation) {
		                //toastr.error(resp.data.validation)
		            }

		            scope.inlineRoleEdit.name = (scope.isOverwritten())
		            									?scope.m.role_name
		            									:scope.m.default_role_name
				}).then(function () {
					scope.inProgress = false;
				})
			};

			function formatRoleName (name) {
				return name?'(' + name + ')':null;
			}
		}
	}
}
