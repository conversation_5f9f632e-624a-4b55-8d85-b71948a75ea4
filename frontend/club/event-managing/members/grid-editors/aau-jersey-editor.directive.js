angular.module('SportWrench').directive('aauJerseyEditor', aauJerseyEditor);

aauJerseyEditor.$inject = ['EventMembersService', 'toastr', 'OVERWRITE_VALUE_MODE', 'DEFAULT_VALUE_MODE'];

function aauJerseyEditor (EventMembersService, toastr, OVERWRITE_VALUE_MODE, DEFAULT_VALUE_MODE) {
	return {
		restrict: 'E',
		scope: {
			m 				: '=member',
			eventId 		: '@',
			onSaved 		: '&?',
			onAddonClick 	: '&',
			block 			: '='
		},
		templateUrl: 'club/event-managing/members/grid-editors/aau-jersey-editor.html',
		link: function (scope) {
			scope.inlineAAUJerseyEdit = {};

			scope.isOverwritten = function () {
				return scope.m.aau_jersey && (scope.m.aau_jersey !== scope.m.default_aau_jersey)
			}

			scope.aauJerseyEditToggle = function (isEditMode, member) {
		        if(isEditMode) {
		        	if(scope.isOverwritten()) {
		        		scope.inlineAAUJerseyEdit.value = member.aau_jersey;
		        		scope.inlineAAUJerseyEdit.type = OVERWRITE_VALUE_MODE;
		        	} else {
		        		scope.inlineAAUJerseyEdit.value = member.default_aau_jersey;
		        		scope.inlineAAUJerseyEdit.type = DEFAULT_VALUE_MODE;
		        	}
		        } else {
		            scope.inlineAAUJerseyEdit.value = undefined;
		            scope.inlineAAUJerseyEdit.type = '';
		        }
		    }

		    scope.saveAAUJerseyChange = function (m) {
		    	if(scope.inProgress) return;
		    	scope.inProgress = true;
		        var body 			= {}, 
		            data 		= scope.inlineAAUJerseyEdit,
		            aau_jersey = data.value,
		            isRosterUpdates = (data.type === OVERWRITE_VALUE_MODE);

		        if(isRosterUpdates && aau_jersey === scope.m.default_aau_jersey) {
		        	body[data.type] = {
                        aau_jersey: null
			        }
		        } else {
		        	body[data.type] = {
                        aau_jersey: aau_jersey
			        }
		        }

                if(!_.isEmpty(body.d)) {
                    if(!_.isNull(body.d.aau_jersey) && body.d.aau_jersey <= 0) {
                        toastr.warning('Uniform must be a positive number');
                        scope.inProgress = false;
                        return;
                    }
                }

                if(!_.isEmpty(body.o)) {
                    if(!_.isNull(body.o.aau_jersey) && body.o.aau_jersey <= 0) {
                        toastr.warning('Uniform must be a positive number');
                        scope.inProgress = false;
                        return;
                    }
                }

		        EventMembersService.updateAthlete(m.id, scope.eventId, body)
		        .then(function () {
		            if(isRosterUpdates) {
		                m.aau_jersey = body[OVERWRITE_VALUE_MODE].aau_jersey
		            } else {
		                m.default_aau_jersey = body[DEFAULT_VALUE_MODE].aau_jersey
		            }
		            scope.$broadcast('inline-edit.close');
		            if(scope.onSaved) scope.onSaved();
		        }, function (resp) {
		            if(resp && resp.data.validation) {
		                toastr.error(resp.data.validation)
		            }
		        }).then(function () {
		        	scope.inProgress = false;
		        })
		    }

		    scope.onEditKeypress = function ($event, m) {
		        if($event.charCode === 13) {
		            scope.saveAAUJerseyChange(m);
		        }
			}

			scope.getDefaultAAUJersey = function () {
				return (scope.isOverwritten())
							?('(' + scope.m.default_aau_jersey + ')')
							:scope.m.default_aau_jersey
			}
		}
	}
}
