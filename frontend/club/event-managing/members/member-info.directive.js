angular.module('SportWrench').directive('eventMemberInfo', eventMemberInfo);

eventMemberInfo.$inject = ['EventMembersService', '$stateParams', 'toastr', 'INTERNAL_ERROR_MSG', '$state', '$q', 'APP_ROUTES'];

function eventMemberInfo(EventMembersService, $stateParams, toastr, INTERNAL_ERROR_MSG, $state, $q, APP_ROUTES) {
	var JUNIOR_TYPE = 'j';
	return {
		restrict: 'E',
		scope: {
			eventName 		    : '=',
			teamName 		    : '=',
			deadlinePassed 	    : '=',
            rosterLocked        : '=',
			deadline 		    : '=',
            onlineCheckinDate   : '=',
			memberId		    : '=',
			memberType 		    : '=',  // j - for athlete (junior), s - for staff
            clubHasNineManSanctioning : '=',
            eventHasAauSanctioning    : '=',
		},
		templateUrl: 'club/event-managing/members/member-info.html',
		link: function (scope) {
			var eventId = $stateParams.event;
			scope.initialMember = {};
			scope.member = {};
			scope.utils = {};

			__loadMember();

			scope.isJunior = function () {
				return (scope.memberType === JUNIOR_TYPE)
			}

			scope.save = function () {
				scope.utils.error = null;
				var reqBody		= __checkMember();

				if(!reqBody) {
					toastr.warning('Nothing changed');
            		return;
				}

                if(!_.isEmpty(reqBody.d)) {
                    if(
                        (!_.isNull(reqBody.d.jersey) && reqBody.d.jersey <= 0) ||
                        (!_.isNull(reqBody.d.aau_jersey) && reqBody.d.aau_jersey <= 0)
                    ) {
                        toastr.warning('Uniform must be a positive number');
                        return;
                    }
                }

                if(!_.isEmpty(reqBody.o)) {
                    if(
                        (!_.isNull(reqBody.o.jersey) && reqBody.o.jersey <= 0) ||
                        (!_.isNull(reqBody.o.aau_jersey) && reqBody.o.aau_jersey <= 0)
                    ) {
                        toastr.warning('Uniform must be a positive number');
                        return;
                    }
                }

				(
					(scope.memberType === JUNIOR_TYPE)
						?EventMembersService.updateAthlete(scope.memberId, eventId, reqBody)
						:EventMembersService.updateStaff(scope.memberId, eventId, reqBody)
				).then(function () {
					toastr.success('Saved')
					__emitListReloading();
					scope.$parent.$close();
				}, __errorRespHandler)
			}

			scope.withdraw = function () {
				if(!confirm('Do you really want to remove this Member from "' + scope.eventName + '"'))
					return;
				scope.utils.error = null;

				(
					(scope.memberType === JUNIOR_TYPE)
						?EventMembersService.withdrawAthlete(scope.memberId, eventId)
						:EventMembersService.withdrawStaff(scope.memberId, eventId)
				).then(function () {
					__emitListReloading();
					scope.$parent.$close();
				}, __errorRespHandler)
			}

			scope.reinstate = function () {
				scope.utils.error = null;
				(
					(scope.memberType === JUNIOR_TYPE)
						?EventMembersService.reinstateAthlete(scope.memberId, eventId)
						:EventMembersService.reinstateStaff(scope.memberId, eventId)
				).then(function () {
					__loadMember();
					toastr.success('Member is reinstated');
					__emitListReloading();
				}, __errorRespHandler)
			}

			scope.memberLoaded = function () {
				return !_.isEmpty(scope.member)
			}

			scope.openMemberInfo = function () {
				if(scope.memberType === 's') {
					$state.go(APP_ROUTES.CD.STAFF_UPDATE, {
						master_staff_id: scope.member.master_staff_id
					})
				} else {
					scope.$emit('club.athletes.show-info', scope.member.master_athlete_id)
				}
			}

            scope.hideMemberInfoButton = function () {
                return scope.member.deleted_from_club;
            }

			scope.getDeadline = function () {
				return scope.deadline
			}

			function __emitListReloading () {
				scope.$emit('club.event-roster.members.reload');
			}

			function __errorRespHandler (resp) {
				var error = resp && resp.data.validation;
				if(error) {
					scope.utils.error = error;
					//toastr.error(error);
				}
			}

			function __loadMember () {
				(
					(scope.memberType === JUNIOR_TYPE)
						?EventMembersService.findAthlete(scope.memberId, eventId)
						:__loadStaff(scope.memberId, eventId)
				).then(function (member) {
					scope.member = member;
					scope.initialMember = _.clone(scope.member);
				}, __errorRespHandler)
			}

			function __loadStaff (id, eventId) {
				return $q.all([
					EventMembersService.findStaff(id, eventId),
					EventMembersService.getStafferTeamsList(id, eventId)
				]).then(function (result) {
					return _.extend(result[0], result[1])
				})
			}

			function __checkMember () {
				var defaultChanges      = {},
		            overrideChanges     = {};
		        if(scope.memberType === JUNIOR_TYPE) {
		        	// Jersey
			        if(scope.initialMember.default_jersey !== scope.member.default_jersey) {
			            defaultChanges.jersey = scope.member.default_jersey;
			        }
			        if((scope.initialMember.jersey !== scope.member.jersey) && scope.utils.overwrite_jersey) {
			            overrideChanges.jersey = scope.member.jersey;
			        } else if (!scope.utils.overwrite_jersey && scope.member.jersey) {
			        	overrideChanges.jersey = null;
			        }
                    // AAU Jersey
			        if(scope.initialMember.default_aau_jersey !== scope.member.default_aau_jersey) {
			            defaultChanges.aau_jersey = scope.member.default_aau_jersey;
			        }
			        if((scope.initialMember.aau_jersey !== scope.member.aau_jersey) && scope.utils.overwrite_aau_jersey) {
			            overrideChanges.aau_jersey = scope.member.aau_jersey;
			        } else if (!scope.utils.overwrite_aau_jersey && scope.member.aau_jersey) {
			        	overrideChanges.aau_jersey = null;
			        }
			        // Position
			        if(scope.initialMember.default_position !== scope.member.default_position) {
			            defaultChanges.position = scope.member.default_position || null;       
			        }
			        if(
			        	(scope.initialMember.sport_position_id !== scope.member.sport_position_id)
			        	&& scope.utils.overwrite_position) {
			           		overrideChanges.position = scope.member.sport_position_id || null;
			    	} else if(!scope.utils.overwrite_position && scope.member.sport_position_id) {
			    		overrideChanges.position = null;
			    	}
		    	} else {
		    		if(scope.initialMember.primary !== scope.member.primary) {
			            overrideChanges.primary = scope.member.primary || null;
		    		}
			        if(scope.initialMember.default_primary !== scope.member.default_primary) {
			            defaultChanges.primary = scope.member.default_primary || null;
			        }

			        if (
			        	scope.member.primary_master_team.master_team_id !== 
			        	scope.initialMember.primary_master_team.master_team_id) {
			        	defaultChanges.primary_team_id = scope.member.primary_master_team.master_team_id || null;
			        }
			        if (
			        	scope.member.primary_roster_team.roster_team_id !== 
			        	scope.initialMember.primary_roster_team.roster_team_id) {
			        	overrideChanges.primary_team_id = scope.member.primary_roster_team.roster_team_id || null;
			        } else if (!scope.utils.overwrite_primary_team && scope.member.primary_roster_team.roster_team_id) {
			        	overrideChanges.primary_team_id = null;
			        }

			        // Role
			        if(
			        	(scope.initialMember.role_id !== scope.member.role_id) 
			        	&& scope.utils.overwrite_staff_role) {
			            overrideChanges.role_id = scope.member.role_id || null;
			        } else if (!scope.utils.overwrite_staff_role && scope.member.role_id) {
			        	overrideChanges.role_id = null;
			        }
			        if(scope.initialMember.default_role_id !== scope.member.default_role_id) {
			            defaultChanges.role_id = scope.member.default_role_id || null;
			        }
		    	}

		    	if(_.isEmpty(defaultChanges) && _.isEmpty(overrideChanges)) {
		    		return null;
		    	}

		      	return {
		      		d: defaultChanges,
		      		o: overrideChanges
		      	}
			}
		}
	}
}
