angular.module('SportWrench').service('ClubPaymentsService', clubPaymentsService);

clubPaymentsService.$inject = ['$http', '$window', '$uibModal'];

function clubPaymentsService ($http, $window, $uibModal) {
	this.$http 		= $http;
	this.$window 	= $window;
    this._$uibModal = $uibModal;
}

clubPaymentsService.prototype.loadHistory = function (eventId) {
	return this.$http.get('/api/club/event/' + eventId + '/purchase-list');
};

clubPaymentsService.prototype.pay = function (data) {
	return this.$http.post('/api/club/purchase', data);
};

clubPaymentsService.prototype.openReceipt = function (id) {
	var url = '/invoice/' + id + '/club';

	var w = this.$window.open(url, '_blank');

	if(!w) {
		window.location = url;
	}
};

clubPaymentsService.prototype.getPayment = function (eventId, paymentId) {
	return this.$http.get('/api/club/event/' + eventId + '/purchase/' + paymentId);
};

clubPaymentsService.prototype.changeType = function (eventId, paymentId, data) {
	return this.$http.put('/api/club/event/' + eventId + '/purchase/' + paymentId + '/change-type', data);
};

clubPaymentsService.prototype.cancelCheckPayment = function (eventId, paymentId) {
	return this.$http.delete('/api/club/event/' + eventId + '/purchase/' + paymentId + '/cancel');
};

clubPaymentsService.prototype.openBalanceInfoModal = function(payment) {
    return this._$uibModal.open({
        template: '<balance-information-modal on-close="$close()" payment="payment"></balance-information-modal>',
        controller: ['$scope', function ($scope) {
            $scope.payment = payment;
        }]
    })
};
