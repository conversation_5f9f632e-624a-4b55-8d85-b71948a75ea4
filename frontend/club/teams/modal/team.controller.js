angular.module('SportWrench').controller('Club.Teams.Modal.TeamInfoController', TeamInfoController);

TeamInfoController.$inject = ['ClubTeamsService', '$scope', 'INTERNAL_ERROR_MSG', 'teamsService'];

function TeamInfoController (ClubTeamsService, $scope, INTERNAL_ERROR_MSG, teamsService) {
    var self = this;
    this.team = {};
    this.utils = {
        loaded: false
    };

    ClubTeamsService.teamForEditing($scope.master_team_id)
    .success(function (data) {
        self.team               = data.team;
        self.utils.accepted     = data.accepted;
        self.utils.entered      = data.entered;
        self.utils.has_athletes = data.has_athletes;
        self.utils.loaded       = true;

        $scope.$parent.data.team_name = self.team.team_name
    }).error(function (data) {
        self.error = data.validation || INTERNAL_ERROR_MSG
    })

    this.updateTeam = function () {
        this.error = '';
        delete this.team.master_team_id; 

        teamsService.updateMasterTeam(self.team, $scope.master_team_id).then(function (resp) {
            var updTeam = resp.data && resp.data.team || self.team;

            $scope.$emit('club.team.updated', updTeam);
        }).catch(function (resp) {
            self.error = (resp.data && resp.data.validation);
        })       
    }
}
