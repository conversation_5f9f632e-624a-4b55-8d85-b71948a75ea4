angular.module('SportWrench').controller('Club.Teams.Modal.StaffController', StaffController);

StaffController.$inject = ['$scope', 'staffService', '$filter', 'toastr', 'DONE_MSG', 'ROSTER_CHANGE_WARNING_TEXT', 'SANCTIONING_BODY', 'masterClubService'];

function StaffController ($scope, staffService, $filter, toastr, DONE_MSG, ROSTER_CHANGE_WARNING_TEXT,
                          SANCTIONING_BODY, masterClubService) {
    $scope.staff            = [];
    $scope.roles            = [];
    $scope.loaded           = false;
    $scope.originData       = {};
    $scope.warningText      = ROSTER_CHANGE_WARNING_TEXT;
    $scope.SANCTIONING_BODY = SANCTIONING_BODY;
    $scope.filteredStaff = undefined;

    $scope.filters = {
        sanctionedBody: {},
    };

    staffService.getTeamStaff($scope.$parent.master_team_id, function(data) {
        $scope.staff  = data.staff;
        $scope.roles  = $scope.roles.concat(data.roles);
        $scope.loaded = true;

        $scope.originData = angular.copy(data.staff);
    });

    $scope.updateStaffRole = function (s, id) {
        let masterTeamID = $scope.$parent.master_team_id;

        staffService.changeRoleRows(s.master_staff_id, masterTeamID, {role_id: s.role_id}, 'update')
            .then(function() {
                $scope.originData[id].role_id = s.role_id;
                toastr.success(DONE_MSG);
            })
            .catch(function () {
                $scope.staff[id].role_id = $scope.originData[id].role_id;
            })
    }

    $scope.setPrimary = function (isConfirmed, s) {
        if(isConfirmed) {
            staffService.changeRoleRows(s.master_staff_id, $scope.$parent.master_team_id, null, 'primary')
                .then(function () {
                    toastr.success(DONE_MSG);
                    s.primary = true;
                    s.primary_team_name = $scope.$parent.data.team_name;
                });
        }
    }

    $scope.removePrimary = function (isConfirmed, s) {
        if(isConfirmed) {
            staffService.removePrimaryTeam(s.master_staff_id, $scope.$parent.master_team_id).then(function () {
                toastr.success(DONE_MSG);
                s.primary = false;
                s.primary_team_name = undefined;
            });
        }
    }

    $scope.$watch('[staff, filters]', function (newVal) {
        if (newVal[0] && newVal[0].length) {
            $scope.filteredStaff = $filter('filter')(newVal[0], function (value) {
                return masterClubService.sanctionedBodyFilter(value, $scope.filters.sanctionedBody)
            });
        }
    }, true);

    $scope.clubHasUsavAndAauSanctioning = masterClubService.clubHasUsavAndAauSanctioning($scope.club);
}
