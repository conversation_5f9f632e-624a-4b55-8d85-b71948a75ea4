angular.module('SportWrench').controller('ResultReportController', ResultReportController);

ResultReportController.$inject = ['$scope', '$uibModalInstance', '$state', 'ClubTeamsService', 'APP_ROUTES', 'eventsService'];

function ResultReportController(
    $scope,
    $uibModalInstance,
    $state,
    ClubTeamsService,
    APP_ROUTES,
    eventsService
) {
    $scope.teams = [];
    $scope.events = [];

    $scope.selectedTeam = null;
    $scope.selectedEvents = [];

    $scope.dataLoaded = false;

    this.$onInit = () => {
        ClubTeamsService.teamsList({ include_events: true }).success(function (data) {
            $scope.teams = data.teams;
            $scope.selectedTeam = data.teams[0];
            $scope.events = $scope.selectedTeam ? $scope.selectedTeam.events : [];

            $scope.dataLoaded = true;
        });
    }

    $scope.states = {
        update: APP_ROUTES.CD.INFO_UPDATE
    }

    $scope.create = function() {
        if (!$scope.selectedEvents.length || !$scope.selectedTeam) {
            $scope.error = 'Please select and at least one event';
            return;
        }
        $scope.error = '';

        const eventIds = $scope.selectedEvents.map(event => event.event_id);
        const teamId = $scope.selectedTeam.id;

        ClubTeamsService.resultTeamReport(teamId, eventIds).then(() => {
            $uibModalInstance.close();
        });
    }

    $scope.disableExport = function () {
        return !$scope.selectedEvents.length || !$scope.selectedTeam;
    }

    $scope.onEventsUpdate = function() {
        $scope.selectedEvents = $scope.events.filter(event => event.selected);
    }

    $scope.onTeamsUpdate = function(team) {
        $scope.selectedTeam = team;
        $scope.events = team.events || [];
        $scope.events.forEach(event => {
            event.selected = false;
        });
        $scope.selectedEvents = [];
    }
}
