angular.module('SportWrench').controller('Club.Teams.EnteredList', EnteredList)

EnteredList.$inject = ['teamsList', '$uibModalInstance'];

function EnteredList (teamsList, $uibModalInstance) {
    var self = this;
    this.teams = teamsList;
    this.toRemove = [];
    this.forbidRemoval = [];
    this.utils = {
        selection: {}
    }

    this.canRemove = function (t) {
        return t.can_remove
    }

    this.canNotRemove = function (t) {
        return !t.can_remove
    }

    this.toggleSelection = function (id) {
        this.utils.selection[id] = !this.utils.selection[id]
    }

    var __choosen  = function (list) {
        var keys = Object.keys(list), result = [];
        for(var i = 0, l = keys.length; i < l; ++i) {
            if(list[keys[i]]) result.push(parseInt(keys[i], 10))
        }
        return result;
    }

    var __getIdentifiers = function (list) {
        var result = [];
        for(var i = 0, l = list.length; i < l; ++i) {
            result.push(list[i].master_team_id)
        }
        return result;
    }

    var __forbidden = function (list) {
        for(var i = 0, l = self.toRemove.length; i < l; ++i) {
            if(!self.utils.selection[self.toRemove[i].master_team_id])
                list.push(self.toRemove[i].master_team_id)
        }
        return list;
    }

    this.save = function () {
        $uibModalInstance.close({
            remove: __choosen(self.utils.selection),
            forbid: __forbidden(__getIdentifiers(self.forbidRemoval))
        })
    }    

    this.cancel = function () {
        $uibModalInstance.dismiss();
    }
}
