angular.module('SportWrench').controller('CreateStaffController', CreateStaffController);

CreateStaffController.$inject = ['$scope', 'staffService', '$uibModalInstance', '$state', 'APP_ROUTES'];

function CreateStaffController($scope, staffService, $uibModalInstance, $state, APP_ROUTES) {
    $scope.staff = {};        

    $scope.cancel = function() {
        $uibModalInstance.dismiss();
    }

    $scope.create = function() {
        console.log($scope.staff);
        staffService.createStaff($scope.staff, function(resp) {
            $uibModalInstance.close();
            console.log(resp.status);
        })
    }

    $uibModalInstance.result.then(function() {
        $state.transitionTo(APP_ROUTES.CD.STAFF, null, {
            reload: true,
            inherit: true,
            notify: true
        })
    }, function() {
        $state.go(APP_ROUTES.CD.STAFF)
    })
}
