angular.module('SportWrench').controller('Club.MenuController', MenuController);

MenuController.$inject = ['$scope', '$state', 'loadClub', '$log', 'APP_ROUTES', 'USER_ROLE', 'ENV'];

function MenuController ($scope, $state, loadClub, $log, APP_ROUTES, USER_ROLE, ENV) {
    $scope.club                 = loadClub;
    $scope.quick_managing_event = {};
    $scope.filter               = {};

    if(_.isEmpty($scope.club)) {
        $state.go(APP_ROUTES.CD.INFO);   
    }

    $scope.cdRole = USER_ROLE.CD;

    $scope.tabs = [
        { name: 'Club',             state: APP_ROUTES.CD.INFO, isVisible: _alwaysVisible, disabled: _clubExists  },
        { name: 'Teams',            state: APP_ROUTES.CD.TEAMS, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'Enter Events',     state: APP_ROUTES.CD.EVENTS, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'My Teams Events',  state: APP_ROUTES.CD.CLUB_EVENTS, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'Unpaid Events',    state: APP_ROUTES.CD.CLUB_UNPAID_EVENTS, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'Bulk Team Entering', state: APP_ROUTES.CD.CLUB_BULK_REGISTRATION, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'Athletes',         state: APP_ROUTES.CD.ATHLETES, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'Staff',            state: APP_ROUTES.CD.STAFF, isVisible: _alwaysVisible, disabled: _clubExists },
        { name: 'Invoices',         state: APP_ROUTES.CD.CLUB_INVOICES, isVisible: _alwaysVisible, disabled: _clubExists },
        {
            name: _eventName,       state: APP_ROUTES.CD.MANAGE_EVENT_TEAMS, isVisible: _eventManagingVisible, 
            stateParams: _eventManagingStateParams, disabled: _clubExists
        }
    ];

    $scope.clubIsEmpty = function () {
        return _clubExists();
    }

    function _eventName () {
        return $scope.quick_managing_event.name;
    }

    function _clubExists () {
        return _.isEmpty($scope.club);
    }

    function _eventManagingStateParams () {
        return { event: $scope.quick_managing_event.event_id };
    }

    function _alwaysVisible () {
        return true;
    }

    function _eventManagingVisible () {
        return !_.isEmpty($scope.quick_managing_event);
    }

    $scope.exportResults = function (event_id) {
         window.location = '/api/club/event/' + event_id + '/results';
    }
   
    $scope.$on('QUICK_MANAGING_TAB', function(event, managing_event) {
        $scope.quick_managing_event = managing_event;
        if($state.current && $state.current.name && $state.current.name.indexOf(APP_ROUTES.CD.MANAGE_EVENT) < 0) {
            $state.go(APP_ROUTES.CD.MANAGE_EVENT_TEAMS, { event: managing_event.event_id });
        }
        $log.debug('MANAGING EVENT', managing_event);
    });  

    $scope.$on('club.athletes.show-info', function (e, id) {
        $state.go(APP_ROUTES.CD.ATHLETES, { master_athlete_id: id });
    });
}
