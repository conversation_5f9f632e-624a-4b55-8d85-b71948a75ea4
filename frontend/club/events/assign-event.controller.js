angular.module('SportWrench').controller('AssignEventController', AssignEventController);

AssignEventController.$inject = [
    '$scope', 'eventsService', '$stateParams', 'userProfileService', 'USER_ROLE',
    'passEventService', '$uibModalInstance', '$state', 'rosterTeamService', '$timeout',
    '$rootScope', 'previousStateService', 'masterTeamService',
    'userService', 'toastr', 'APP_ROUTES', 'clubNotCompleted'
];

function AssignEventController ($scope, eventsService, $stateParams, userProfileService, USER_ROLE,
    passEventService, $uibModalInstance, $state, rosterTeamService, $timeout,
    $rootScope, previousStateService, masterTeamService,
    userService, toastr, APP_ROUTES, clubNotCompleted
) {
    $scope.event = passEventService.get();
    $scope.event_info = {};
    $scope.roster_teams = [];
    $scope.utils = {
        selection: {},
        entered: 0,
        accepted: 0,
        maximum: 0
    }

    var doNotGoBack = false;

    $scope.clubHasTeams = false;

    // check if club has teams
    masterTeamService.count({ deleted: false }, function (count) {
        $scope.clubHasTeams = (count > 0);
    });


    eventsService.club_owner.getEventByIdWithLocation($stateParams.event, function(resp) {
        $scope.event_info = resp.data.event;
    });

    $scope.cancel = function() {
        $uibModalInstance.dismiss();
    };

    $scope.toManageMenu = function() {
        if(clubNotCompleted) {
            $scope.cancel();
            return userProfileService.openUserProfileCompletenessErrorModal(USER_ROLE.CD);
        } else {
            $scope.manageEventTeams($stateParams.event, $scope.event.long_name);
        }
    };
    // There is a bug whith tab select directive in UI-ROUTER
    $scope.load_roster_teams = function () {
        rosterTeamService.club_owner_menu
                .event_teams_list($stateParams.event)
        .success(function (data) {
            $scope.event_divisions = data.divisions;
            $scope.utils.entered = data.entered;
            $scope.utils.accepted = data.accepted;
            $scope.utils.maximum = data.maximum;
        }).error(function (data) {
            $scope.event_divisions = null;
            var msg = data && (data.validation || data.error);
            if(msg) toastr.error(msg)
        })
    }

    $scope.chooseDivision = function (d) {
        if(!d.teams.length) return;
        this.utils.selection[d.division_id] = !this.utils.selection[d.division_id]
    }

    $scope.isLoggedIn = function () {
        return userService.isLoggedIn();
    }

    $scope.showTeamsTab = function () {
        return (!_.isEmpty(this.event_info))
    }

    $scope.manageEventTeams = function(event, name) {
        $uibModalInstance.close(event);
        $rootScope.$broadcast('QUICK_MANAGING_TAB', {
            event_id: event,
            name: name
        });
    };

    $scope.toTeamCreation = function() {
        doNotGoBack = true;
        $state.transitionTo(APP_ROUTES.CD.TEAMS, null, {
            reload      : true,
            inherit     : true,
            notify      : true
        });
    };

    $scope.showEnterEventBtn = function() {
        return !$scope.event_info.club_entered && ($scope.event_info.teams_entry_sw_fee !== null);
    };

    $scope.showTeamsNumber = function() {
        return $scope.event_info.show_number_of_teams_for_cd;
    }

    $uibModalInstance.result.then(function(result) {
            var prev_state = previousStateService.get();
            if (result && prev_state.name) {
                // $state.go(APP_ROUTES.CD.MANAGE_EVENT_TEAMS,{event: result});
            } else {
                $state.transitionTo(APP_ROUTES.CD.EVENTS, null, {
                    reload: (prev_state.name)?false:true,
                    inherit: true,
                    notify: true
                });
            }
        }, function () {
            if (doNotGoBack) {
                return;
            }

            var prev_state = previousStateService.get();
            $state.transitionTo(prev_state.name || APP_ROUTES.CD.EVENTS, prev_state.params, {
                reload: (prev_state.name)?false:true,
                inherit: true,
                notify: true
            });
        });
}
