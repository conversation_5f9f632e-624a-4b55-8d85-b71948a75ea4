angular.module('SportWrench').controller('Doubles.AssignController', AssignController);

AssignController.$inject = ['$scope', '$stateParams', 'doublesService', '$log', '$state', 'APP_ROUTES'];

function AssignController ($scope, $stateParams, doublesService, $log, $state, APP_ROUTES) {
    $scope.data = {
        event           : {},
        player_one      : {},
        player_two      : {},
        division_info   : {},
        paid            : false,
        hash            : null
    };

    $scope.error = {};

    doublesService.getEventInfo($stateParams.event, function(data) {
        $scope.data.event = data.event;
    }, function(data) {
        $scope.error.text = data.error;
    });

    if( $state.is(APP_ROUTES.D.PARENT) ) $state.go(APP_ROUTES.D.TYPE);

    $scope.getTeamName = function () {
        var _p1 = $scope.data.player_one;
        var _p2 = $scope.data.player_two;

        if(_p1.last && _p2.last) {
            if(_p1.age && _p2.age) {
                return ( _p1.last + ' (' + _p1.age + ') / ' + _p2.last + ' (' + _p2.age + ')' );
            } else {
                return ( _p1.last + ' / ' + _p2.last );
            }
        }
        return null;
    }

    $scope.moveBack = function () {
        $state.go(APP_ROUTES.D.REGISTER)
    }

    $scope.showPrevStateNav = function () {
        return ( $state.is(APP_ROUTES.D.REGISTER) ) && !$scope.data.paid;
    }
}
