angular.module('SportWrench').controller('Doubles.TeamAgesController', TeamAgesController);

TeamAgesController.$inject = ['$scope', '$state', 'APP_ROUTES'];

function TeamAgesController ($scope, $state, APP_ROUTES) {
    $scope.types = [
        { name: 'Adult team', id: 'adult' },
        { name: 'Junior team', id: 'junior' }
    ];
    $scope.next = function () {
        $state.go(APP_ROUTES.D.REGISTER);
    }
}
