angular.module('SportWrench').controller('Doubles.RegisterController', RegisterController);

RegisterController.$inject = ['$scope', 'doublesService', '$stateParams', '$log', '$state', 'APP_ROUTES'];

function RegisterController ($scope, doublesService, $stateParams, $log, $state, APP_ROUTES) {
    $scope.data = {};

    if(!$scope.$parent.data.event.divisions)
        $state.go(APP_ROUTES.D.TYPE);

    $scope.athleteAge = function (a) {
        return ($scope.$parent.data.team_type === 'adult')?'Adult':a.age
    }

    var _minAge = function () {
        var a1 = $scope.$parent.data.player_two.age, 
            a2 = $scope.$parent.data.player_one.age;
        return (a1 > a2)?a2:a1
    }

    $scope.getNotesText = function () {        
        return ($scope.$parent.data.team_type === 'adult')
                    ?'You can enter only Adult divisions'
                    :'Minimum division\'s age allowed for your team is ' + _minAge()
    }
        
    $scope.divisionLabel = function (d) {
        return d.name + ' ($' + d.fee + ')'
    }

    $scope.register = function () {   
        doublesService.register($stateParams.event, $scope.$parent.data.division, function(data) {
            $scope.$parent.data.division_info = data.data;
        });
    }
}
