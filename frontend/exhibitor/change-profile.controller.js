angular.module('SportWrench').controller('Exhibitor.ChangeProfileCtrl', ChangeProfileCtrl);

ChangeProfileCtrl.$inject = [
    '$scope', '$state', '$stateParams', 'userService', '$http', 'toastr', 'INVALID_FORM_ERROR_MSG',
    '_', '$uibModalInstance', 'APP_ROUTES'
];

function ChangeProfileCtrl (
	$scope, $state, $stateParams, userService, $http, toastr, INVALID_FORM_ERROR_MSG, _, $uibModalInstance,
	APP_ROUTES
) {
	var isCreateMode 	= $state.is(APP_ROUTES.EX.PROFILE_CREATE),
		getProfileUrl 	= '/api/sponsor/profile',
		updUrl 			= getProfileUrl + ((!!$stateParams.exhibitor)?('/' + $stateParams.exhibitor):'');

	$scope.profile 	= {};

	$scope.emailPattern = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))/;

	$scope.submit = function () {
		if($scope.profileForm.$invalid) {
			toastr.warning(INVALID_FORM_ERROR_MSG);
			return;
		}

		$scope.profileForm.$setSubmitted();

		var profile = _.omit($scope.profile, 'gender');

		(
			(isCreateMode)
				?$http.post(getProfileUrl, profile)
				:$http.put(updUrl, profile)
		).then(function () {
			$uibModalInstance.close();
		});
	};

    loadProfileData();

    function loadProfileData () {
    	if(isCreateMode) {
    		userService.getUser(function(user) {
		        $scope.profile = {
		            first			: user.first,
		            last 			: user.last,
		            email 			: user.email,
		            mobile_phone 	: user.phone_mob,
		            zip 			: user.zip
		        };
		    });
    	} else {
    		$http.get(updUrl)
    		.then(function (resp) {
    			$scope.profile = resp.data;
    			if(resp.data.samples_food || resp.data.samples_beverages) {
    				$scope.profile.company_samples = true;
    			}
    		});
    	}
    }
}
