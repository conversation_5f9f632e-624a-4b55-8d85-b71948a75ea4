angular.module('SportWrench').controller('Exhibitor.ProfileController', ExhibitorProfileController);

ExhibitorProfileController.$inject = ['$scope', '$http', '$rootScope', 'geoService', '$state', 'APP_ROUTES'];

function ExhibitorProfileController($scope, $http, $rootScope, geoService, $state, APP_ROUTES) {

    $scope.states = [];

    let routes = {
    	create: APP_ROUTES.EX.PROFILE_CREATE,
    	update: APP_ROUTES.EX.PROFILE_UPDATE
    };

    geoService.getStates('US', function(res) {
        $scope.states = res.data;
    });

    $scope.state = $scope.profileExists ? routes.update : routes.create;
}
