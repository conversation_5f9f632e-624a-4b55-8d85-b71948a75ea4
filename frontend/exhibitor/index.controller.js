angular.module('SportWrench').controller('Exhibitor.IndexController', ExhibitorIndexController);

ExhibitorIndexController.$inject = ['$scope', '$state', '$http', '$location', '_', 'APP_ROUTES'];

function ExhibitorIndexController($scope, $state, $http, $location, _, APP_ROUTES) {
    $scope.profileExists    = false;
    $scope.profile          = {};

    $scope.tabs = [
        { name: 'Exhibitor / Sponsor Profile',      state: APP_ROUTES.EX.PROFILE,   isVisible: visible },
        { name: 'Events',     state: APP_ROUTES.EX.EVENTS,  isVisible: isProfileExists },
        { name: 'Invoices',     state: APP_ROUTES.EX.RECEIPTS,  isVisible: isProfileExists },
    ];

    function visible () {
        return true;
    }

    function isProfileExists () {
        return $scope.profileExists;
    }

    $http.get('/api/sponsor/profile')
    .success(function(data) {
        if (_.isEmpty(data)) {
            $scope.profileExists = false;
            $state.go(APP_ROUTES.EX.PROFILE);
            return;
        }

        $scope.profileExists = true;
        $scope.profile = data;
        $state.go(APP_ROUTES.EX.EVENTS);
    });
}
